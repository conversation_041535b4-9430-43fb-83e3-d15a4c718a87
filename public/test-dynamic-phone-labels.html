<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JobbLogg - Dynamic Phone Field Labels</title>
    <style>
        body {
            font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1f2937;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        .header h1 {
            color: #2563eb;
            margin: 0 0 10px 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        .header p {
            color: #6b7280;
            font-size: 1.1rem;
            margin: 0;
        }
        .success-banner {
            background: #f0fdf4;
            border: 2px solid #bbf7d0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .success-banner h2 {
            color: #166534;
            margin: 0 0 10px 0;
            font-size: 1.5rem;
        }
        .success-banner p {
            color: #15803d;
            margin: 0;
            font-weight: 500;
        }
        .feature-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        .feature-section h2 {
            color: #1f2937;
            margin: 0 0 20px 0;
            font-size: 1.5rem;
            font-weight: 600;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #d1d5db;
        }
        .feature-card h3 {
            color: #2563eb;
            margin: 0 0 10px 0;
            font-size: 1.2rem;
            font-weight: 600;
        }
        .feature-card p {
            color: #4b5563;
            margin: 0 0 15px 0;
            font-size: 0.95rem;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .feature-list li {
            padding: 5px 0;
            color: #6b7280;
            font-size: 0.9rem;
        }
        .feature-list li:before {
            content: "✅ ";
            margin-right: 8px;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.85rem;
            overflow-x: auto;
            margin: 15px 0;
        }
        .scenario-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .scenario {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .scenario.mobile {
            background: #f0f9ff;
            border-color: #0ea5e9;
        }
        .scenario.landline {
            background: #fef3c7;
            border-color: #f59e0b;
        }
        .scenario.manual {
            background: #f3f4f6;
            border-color: #6b7280;
        }
        .scenario h4 {
            margin: 0 0 10px 0;
            font-size: 1.1rem;
            font-weight: 600;
        }
        .scenario.mobile h4 {
            color: #0c4a6e;
        }
        .scenario.landline h4 {
            color: #92400e;
        }
        .scenario.manual h4 {
            color: #374151;
        }
        .scenario .api-data {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
            padding: 8px;
            font-family: monospace;
            font-size: 0.8rem;
            margin: 10px 0;
        }
        .scenario .label-display {
            background: #ffffff;
            border: 2px solid #d1d5db;
            border-radius: 6px;
            padding: 12px;
            margin: 10px 0;
            font-weight: 600;
        }
        .scenario.mobile .label-display {
            border-color: #0ea5e9;
            color: #0c4a6e;
        }
        .scenario.landline .label-display {
            border-color: #f59e0b;
            color: #92400e;
        }
        .scenario.manual .label-display {
            border-color: #6b7280;
            color: #374151;
        }
        .test-instructions {
            background: #eff6ff;
            border: 2px solid #bfdbfe;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-instructions h3 {
            color: #1e40af;
            margin: 0 0 15px 0;
            font-size: 1.3rem;
        }
        .test-instructions ol {
            color: #1e3a8a;
            margin: 0;
            padding-left: 20px;
        }
        .test-instructions li {
            margin: 8px 0;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 Dynamic Phone Field Labels</h1>
            <p>Smart phone field labels based on Brønnøysundregisteret data source</p>
        </div>

        <div class="success-banner">
            <h2>✅ DYNAMIC LABELS IMPLEMENTED</h2>
            <p>Phone field labels now adapt automatically based on registry data source</p>
        </div>

        <!-- Label Logic -->
        <div class="feature-section">
            <h2>🏷️ Dynamic Label Logic</h2>
            <p>The phone field label changes automatically based on the data source from Brønnøysundregisteret:</p>
            
            <div class="scenario-grid">
                <div class="scenario mobile">
                    <h4>📱 Mobile Number</h4>
                    <div class="api-data">enhet.mobil: "+4793209260"</div>
                    <div class="label-display">Mobilnummer</div>
                    <p>When auto-populated from the <code>mobil</code> field</p>
                </div>
                
                <div class="scenario landline">
                    <h4>📞 Landline Number</h4>
                    <div class="api-data">enhet.telefon: "+4722123456"</div>
                    <div class="label-display">Telefonnummer</div>
                    <p>When auto-populated from the <code>telefon</code> field</p>
                </div>
                
                <div class="scenario manual">
                    <h4>✏️ Manual Entry</h4>
                    <div class="api-data">No registry data</div>
                    <div class="label-display">Mobilnummer</div>
                    <p>Default label for manual entry (most common case)</p>
                </div>
            </div>
        </div>

        <!-- Technical Implementation -->
        <div class="feature-section">
            <h2>⚙️ Technical Implementation</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🔧 Enhanced CompanyInfo Interface</h3>
                    <div class="code-block">
registryContact?: {
  phone?: string;
  email?: string;
  /** Source of phone number: 'mobil' for mobile, 'telefon' for landline */
  phoneSource?: 'mobil' | 'telefon';
};
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>📊 API Data Extraction</h3>
                    <div class="code-block">
// Prioritize mobile over landline
const phoneNumber = enhet.mobil || enhet.telefon;
const phoneSource: 'mobil' | 'telefon' | undefined = 
  enhet.mobil ? 'mobil' : (enhet.telefon ? 'telefon' : undefined);

if (phoneNumber || enhet.epostadresse) {
  company.registryContact = {
    phone: phoneNumber,
    email: enhet.epostadresse,
    phoneSource: phoneSource
  };
}
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>🏷️ Dynamic Label Function</h3>
                    <div class="code-block">
// Get dynamic phone field label based on registry data source
const getPhoneLabel = (): string => {
  if (brregData?.registryContact?.phoneSource === 'mobil') {
    return 'Mobilnummer';
  } else if (brregData?.registryContact?.phoneSource === 'telefon') {
    return 'Telefonnummer';
  }
  return 'Mobilnummer'; // Default for manual entry
};

// Get phone field description
const getPhoneDescription = (): string => {
  const label = getPhoneLabel().toLowerCase();
  return `Norsk ${label} med +47 landskode`;
};
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>✅ Enhanced Validation</h3>
                    <div class="code-block">
// Different validation based on phone type
const phoneSource = brregData?.registryContact?.phoneSource;
if (phoneSource === 'mobil' || !phoneSource) {
  // Mobile number validation (starts with 4, 5, 9)
  if (!/^[4-9]/.test(digits)) {
    newErrors.phone = 'Ugyldig norsk mobilnummer';
  }
} else if (phoneSource === 'telefon') {
  // Landline validation (typically starts with 2, 3, 5, 6, 7)
  if (!/^[2-7]/.test(digits)) {
    newErrors.phone = 'Ugyldig norsk telefonnummer';
  }
}
                    </div>
                </div>
            </div>
        </div>

        <!-- UI Components -->
        <div class="feature-section">
            <h2>🎨 UI Component Updates</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🔒 LockedInput (Auto-populated)</h3>
                    <div class="code-block">
&lt;LockedInput
  label={getPhoneLabel()}  // Dynamic: "Mobilnummer" or "Telefonnummer"
  value={`+47 ${formData.phone.replace(/(\d{3})(\d{2})(\d{3})/, '$1 $2 $3')}`}
  fullWidth
  helperText="Hentet automatisk fra Brønnøysundregisteret og kan ikke endres"
/&gt;

&lt;span className="text-sm text-jobblogg-text-muted"&gt;
  Vil du endre {getPhoneLabel().toLowerCase()}?  // Dynamic question
&lt;/span&gt;
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>📱 PhoneInput (Manual Entry)</h3>
                    <div class="code-block">
&lt;PhoneInput
  label={getPhoneLabel()}  // Dynamic: "Mobilnummer" or "Telefonnummer"
  required
  fullWidth
  value={formData.phone}
  onChange={handlePhoneChange}
  error={errors.phone}
  helperText={getPhoneDescription()}  // Dynamic description
/&gt;
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>📋 Information Section</h3>
                    <div class="code-block">
&lt;p&gt;
  • &lt;strong&gt;{getPhoneLabel()}:&lt;/strong&gt; Brukes for direkte kontakt og vises i prosjektdetaljer
&lt;/p&gt;
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>⚠️ Validation Messages</h3>
                    <div class="code-block">
// Dynamic validation messages
if (!formData.phone.trim()) {
  newErrors.phone = getPhoneValidationError();  // "Mobilnummer er påkrevd" or "Telefonnummer er påkrevd"
} else if (digits.length !== 8) {
  newErrors.phone = `${getPhoneLabel()} må være 8 siffer`;  // Dynamic label in error
}
                    </div>
                </div>
            </div>
        </div>

        <!-- Testing Scenarios -->
        <div class="feature-section">
            <h2>🧪 Testing Scenarios</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>📱 Mobile Number Test</h3>
                    <ul class="feature-list">
                        <li>Search for company with mobile number in API</li>
                        <li>Verify label shows "Mobilnummer"</li>
                        <li>Check auto-population works correctly</li>
                        <li>Verify validation uses mobile number rules</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>📞 Landline Number Test</h3>
                    <ul class="feature-list">
                        <li>Search for company with only landline number</li>
                        <li>Verify label shows "Telefonnummer"</li>
                        <li>Check fallback to telefon field works</li>
                        <li>Verify validation uses landline rules</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>✏️ Manual Entry Test</h3>
                    <ul class="feature-list">
                        <li>Search for company with no phone data</li>
                        <li>Verify label defaults to "Mobilnummer"</li>
                        <li>Check manual entry works normally</li>
                        <li>Verify standard mobile validation applies</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🔄 Toggle Override Test</h3>
                    <ul class="feature-list">
                        <li>Auto-populate phone from registry</li>
                        <li>Use toggle to switch to manual entry</li>
                        <li>Verify label remains consistent</li>
                        <li>Check helper text updates correctly</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Testing Instructions -->
        <div class="test-instructions">
            <h3>🧪 Testing Instructions</h3>
            <ol>
                <li><strong>Mobile Number Test:</strong> Search for a company that has a mobile number in the `mobil` field</li>
                <li><strong>Verify Mobile Label:</strong> Go to Step 3 and confirm the field shows "Mobilnummer"</li>
                <li><strong>Landline Number Test:</strong> Find a company with only a landline number in the `telefon` field</li>
                <li><strong>Verify Landline Label:</strong> Confirm the field shows "Telefonnummer" for landline numbers</li>
                <li><strong>Manual Entry Test:</strong> Test with a company that has no phone data in the registry</li>
                <li><strong>Verify Default Label:</strong> Confirm manual entry defaults to "Mobilnummer"</li>
                <li><strong>Toggle Test:</strong> Test the override toggle and verify labels remain consistent</li>
                <li><strong>Validation Test:</strong> Verify error messages use the correct dynamic terminology</li>
            </ol>
        </div>

        <!-- Success Banner -->
        <div class="success-banner">
            <h2>🎉 DYNAMIC PHONE LABELS COMPLETE</h2>
            <p>Phone field labels now intelligently adapt to the data source from Brønnøysundregisteret!</p>
        </div>
    </div>
</body>
</html>
