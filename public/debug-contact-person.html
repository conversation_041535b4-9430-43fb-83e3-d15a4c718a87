<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Contact Person Names</title>
    <style>
        body {
            font-family: 'Inter', system-ui, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .debug-section {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .debug-title {
            font-size: 16px;
            font-weight: 600;
            color: #0c4a6e;
            margin-bottom: 10px;
        }
        .debug-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 10px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .test-button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #1d4ed8;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
        }
        .status.error {
            background: #fef2f2;
            color: #dc2626;
        }
        .status.warning {
            background: #fef3c7;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Contact Person Names in Reactions</h1>
        
        <div class="debug-section">
            <div class="debug-title">Test Instructions</div>
            <p>1. Open browser developer console (F12)</p>
            <p>2. Go to shared project page: <a href="http://localhost:5173/shared/k29uOvSMT-" target="_blank">http://localhost:5173/shared/k29uOvSMT-</a></p>
            <p>3. Look for debug messages in console</p>
            <p>4. Try adding a reaction to a message</p>
            <p>5. Check if contact person name appears in reaction tooltip</p>
        </div>

        <div class="debug-section">
            <div class="debug-title">Expected Debug Output</div>
            <div class="debug-output">✅ SharedProject - project.customer: {
  name: "PATOGEN AS",
  contactPerson: "Robert Hansen",
  phone: "96644444",
  email: "<EMAIL>",
  ...
}

✅ EmbeddedChatContainer has customerInfo: {
  name: "PATOGEN AS", 
  contactPerson: "Robert Hansen",
  ...
}

✅ contactPerson found: Robert Hansen

✅ Mapped userId YHtFuM0HNX-Eb6Ua to contactPerson: Robert Hansen

🔍 Final userNames mapping: {
  "YHtFuM0HNX-Eb6Ua": "Robert Hansen",
  "user_2z2JPWDy6cWxcTfNSjM4zQBYJRg": "Leverandør"
}</div>
        </div>

        <div class="debug-section">
            <div class="debug-title">Test Scenarios</div>
            
            <div class="status success">
                <strong>✅ Expected Result:</strong> Reaction tooltips should show "Robert Hansen reagerte med 👍"
            </div>
            
            <div class="status error">
                <strong>❌ Current Problem:</strong> Reaction tooltips show "Anonym reagerte med 👍"
            </div>
            
            <div class="status warning">
                <strong>⚠️ Debug Steps:</strong>
                <br>1. Check if customerInfo is passed to EmbeddedChatContainer
                <br>2. Check if contactPerson field exists in customerInfo
                <br>3. Check if userNames mapping is created correctly
                <br>4. Check if userNames is passed to EnhancedEmojiReactions
                <br>5. Check if reaction tooltip uses correct user name
            </div>
        </div>

        <div class="debug-section">
            <div class="debug-title">Quick Actions</div>
            <button class="test-button" onclick="openSharedProject()">Open Shared Project</button>
            <button class="test-button" onclick="openDevTools()">Open Dev Tools</button>
            <button class="test-button" onclick="checkConsole()">Check Console</button>
        </div>

        <div class="debug-section">
            <div class="debug-title">Data Flow Verification</div>
            <div id="dataFlowStatus">
                <div class="status warning">Waiting for test results...</div>
            </div>
        </div>
    </div>

    <script>
        function openSharedProject() {
            window.open('http://localhost:5173/shared/k29uOvSMT-', '_blank');
        }

        function openDevTools() {
            alert('Press F12 to open Developer Tools, then go to Console tab');
        }

        function checkConsole() {
            console.log('🔍 Debug Contact Person Names Test');
            console.log('Expected customer data:');
            console.log({
                name: "PATOGEN AS",
                contactPerson: "Robert Hansen",
                phone: "96644444",
                email: "<EMAIL>"
            });
            
            const statusDiv = document.getElementById('dataFlowStatus');
            statusDiv.innerHTML = `
                <div class="status warning">
                    <strong>Check browser console for debug output</strong><br>
                    Look for messages starting with 🔍, ✅, or ❌
                </div>
            `;
        }

        // Monitor console for debug messages
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            
            // Check for our debug messages
            const message = args.join(' ');
            if (message.includes('EmbeddedChatContainer has customerInfo') || 
                message.includes('contactPerson found') ||
                message.includes('Final userNames mapping')) {
                
                const statusDiv = document.getElementById('dataFlowStatus');
                const timestamp = new Date().toLocaleTimeString();
                statusDiv.innerHTML += `
                    <div class="status success">
                        <strong>[${timestamp}]</strong> ${message}
                    </div>
                `;
            }
        };

        // Initial setup
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 Debug Contact Person Names - Ready for testing');
            console.log('📋 Test checklist:');
            console.log('1. ✅ Debug page loaded');
            console.log('2. ⏳ Open shared project page');
            console.log('3. ⏳ Check for customerInfo debug output');
            console.log('4. ⏳ Add reaction to message');
            console.log('5. ⏳ Verify contact person name in tooltip');
        });
    </script>
</body>
</html>
