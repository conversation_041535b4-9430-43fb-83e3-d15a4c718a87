<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Enhanced Like Functionality - JobbLogg</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #1f2937;
            margin-bottom: 8px;
        }
        .subtitle {
            color: #6b7280;
            margin-bottom: 24px;
        }
        .test-section {
            background: #eff6ff;
            border-left: 4px solid #2563eb;
            padding: 16px;
            margin: 16px 0;
        }
        .test-section h3 {
            margin: 0 0 8px 0;
            color: #1e40af;
        }
        .test-result {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 12px;
            margin-top: 16px;
            font-family: monospace;
            font-size: 13px;
        }
        .test-result.success {
            background: #f0fdf4;
            border-color: #bbf7d0;
            color: #16a34a;
        }
        .test-result.error {
            background: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        button:hover {
            background: #1d4ed8;
        }
        button.secondary {
            background: #6b7280;
        }
        button.secondary:hover {
            background: #4b5563;
        }
        .feature-list {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
        }
        .feature-list h4 {
            margin: 0 0 12px 0;
            color: #374151;
        }
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin-bottom: 8px;
            color: #4b5563;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Test Enhanced Like Functionality</h1>
        <p class="subtitle">Test the new personalized like display functionality in JobbLogg shared projects</p>

        <div class="feature-list">
            <h4>✨ New Features Implemented:</h4>
            <ul>
                <li><strong>Single Like Personalization:</strong> "Du liker dette" for current user</li>
                <li><strong>Customer Name Display:</strong> "Kontaktperson [Name] liker dette" for other customers</li>
                <li><strong>Fallback Text:</strong> "En kunde liker dette" when name unavailable</li>
                <li><strong>Multiple Likes:</strong> Numerical count display (2, 3, etc.)</li>
                <li><strong>Norwegian Localization:</strong> Proper Norwegian grammar and terminology</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>Test 1: Access Shared Project</h3>
            <p>Navigate to a shared project to test the enhanced like functionality.</p>
            <button onclick="openSharedProject()">Open Shared Project</button>
            <button class="secondary" onclick="openDashboard()">Open Dashboard</button>
            <div id="nav-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>Test 2: Like Functionality Scenarios</h3>
            <p>Test different like scenarios to verify personalized display.</p>
            <button onclick="testLikeScenarios()">View Test Scenarios</button>
            <div id="like-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>Test 3: Customer Name Resolution</h3>
            <p>Verify that customer names are properly extracted from project data.</p>
            <button onclick="testCustomerNames()">Test Customer Names</button>
            <div id="customer-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>Test 4: Mobile Responsiveness</h3>
            <p>Test the enhanced like display on different screen sizes.</p>
            <button onclick="testResponsive()">Test Responsive Design</button>
            <div id="responsive-result" class="test-result" style="display: none;"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, message, isSuccess = false, isError = false) {
            const result = document.getElementById(elementId);
            result.textContent = message;
            result.style.display = 'block';
            result.className = 'test-result';
            if (isSuccess) result.className += ' success';
            if (isError) result.className += ' error';
        }

        function openSharedProject() {
            window.open('http://localhost:5173', '_blank');
            showResult('nav-result', 
                '✅ Opened JobbLogg dashboard.\n\n' +
                'To test enhanced like functionality:\n' +
                '1. Navigate to any project and enable sharing\n' +
                '2. Copy the shared project link\n' +
                '3. Open the shared link in an incognito window\n' +
                '4. Like images to test personalized display\n' +
                '5. Check different scenarios:\n' +
                '   - Single like by current user: "Du liker dette"\n' +
                '   - Single like by other customer: "Kontaktperson [Name] liker dette"\n' +
                '   - Multiple likes: "2 likes", "3 likes", etc.\n\n' +
                'The enhanced functionality is now active!', 
                true
            );
        }

        function openDashboard() {
            window.open('http://localhost:5173', '_blank');
            showResult('nav-result', '✅ Opened dashboard for project navigation.', true);
        }

        function testLikeScenarios() {
            showResult('like-result', 
                '🧪 Enhanced Like Display Test Scenarios:\n\n' +
                '📱 Single Like Scenarios:\n' +
                '• Current user likes image → "Du liker dette"\n' +
                '• Other customer likes image → "Kontaktperson [CustomerName] liker dette"\n' +
                '• Customer name unavailable → "En kunde liker dette"\n\n' +
                '📊 Multiple Like Scenarios:\n' +
                '• 2 customers like image → "2 likes"\n' +
                '• 3+ customers like image → "3 likes", "4 likes", etc.\n\n' +
                '🔧 Technical Implementation:\n' +
                '• PersonalizedLikeDisplay component handles logic\n' +
                '• Customer name extracted from project.customer.name\n' +
                '• Session ID comparison for "Du liker dette"\n' +
                '• Fallback handling for missing data\n\n' +
                'Test by liking images in shared project view!', 
                true
            );
        }

        function testCustomerNames() {
            showResult('customer-result', 
                '👤 Customer Name Resolution Testing:\n\n' +
                '🔍 Data Sources:\n' +
                '• Primary: project.customer.name field\n' +
                '• Fallback: "En kunde liker dette"\n\n' +
                '📋 Test Cases:\n' +
                '• Project with customer name → Shows actual name\n' +
                '• Project without customer → Shows fallback text\n' +
                '• Empty/null customer name → Shows fallback text\n\n' +
                '🎯 Expected Behavior:\n' +
                '• "Kontaktperson Acme Corp liker dette"\n' +
                '• "Kontaktperson John Doe liker dette"\n' +
                '• "En kunde liker dette" (fallback)\n\n' +
                'Customer names are resolved from the shared project data!', 
                true
            );
        }

        function testResponsive() {
            showResult('responsive-result', 
                '📱 Enhanced Like Display Responsive Testing:\n\n' +
                '🎨 Design Consistency:\n' +
                '• Uses existing TextMuted component\n' +
                '• Maintains JobbLogg design system\n' +
                '• Same styling as original like count\n\n' +
                '📐 Mobile Considerations:\n' +
                '• Text fits in existing containers\n' +
                '• Norwegian text length handled properly\n' +
                '• Touch targets remain accessible\n\n' +
                '🔤 Text Length Handling:\n' +
                '• "Du liker dette" (short)\n' +
                '• "Kontaktperson [Name] liker dette" (variable)\n' +
                '• "En kunde liker dette" (medium)\n' +
                '• Numerical counts (shortest)\n\n' +
                'Test by resizing browser or using device emulation!', 
                true
            );
        }
    </script>
</body>
</html>
