<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Company Lookup Test - JobbLogg</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #1f2937;
        }
        input, button {
            padding: 10px 15px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            cursor: pointer;
            margin-left: 10px;
        }
        button:hover {
            background: #1d4ed8;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .results {
            margin-top: 15px;
            padding: 15px;
            background: #f9fafb;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        .error {
            background: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
        }
        .success {
            background: #f0fdf4;
            border-color: #bbf7d0;
            color: #16a34a;
        }
        .loading {
            background: #eff6ff;
            border-color: #bfdbfe;
            color: #2563eb;
        }
        .company-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        .company-name {
            font-weight: 600;
            color: #1f2937;
        }
        .company-details {
            font-size: 13px;
            color: #6b7280;
            margin-top: 5px;
        }
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            margin-top: 5px;
        }
        .status-active {
            background: #dcfce7;
            color: #16a34a;
        }
        .status-inactive {
            background: #fef3c7;
            color: #d97706;
        }
        .status-dissolved {
            background: #fee2e2;
            color: #dc2626;
        }
        pre {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 Company Lookup Test</h1>
        <p>Test the Norwegian Business Register (Brønnøysundregisteret) integration</p>

        <!-- Search by Name Test -->
        <div class="test-section">
            <h3>🔍 Search by Company Name</h3>
            <div>
                <input type="text" id="searchInput" placeholder="F.eks. Equinor, DNB, Telenor..." style="width: 300px;">
                <button onclick="testSearch()">Search</button>
                <button onclick="clearResults()">Clear</button>
            </div>
            <div id="searchResults" class="results" style="display: none;"></div>
        </div>

        <!-- Lookup by Org Number Test -->
        <div class="test-section">
            <h3>🔢 Lookup by Organization Number</h3>
            <div>
                <input type="text" id="orgNumberInput" placeholder="F.eks. ********* (Equinor)" style="width: 300px;">
                <button onclick="testOrgLookup()">Lookup</button>
                <button onclick="clearOrgResults()">Clear</button>
            </div>
            <div id="orgResults" class="results" style="display: none;"></div>
        </div>

        <!-- Quick Test Buttons -->
        <div class="test-section">
            <h3>⚡ Quick Tests</h3>
            <button onclick="quickTest('Equinor')">Test Equinor</button>
            <button onclick="quickTest('DNB')">Test DNB</button>
            <button onclick="quickTest('Telenor')">Test Telenor</button>
            <button onclick="quickOrgTest('*********')">Test Equinor Org#</button>
            <button onclick="quickOrgTest('*********')">Test DNB Org#</button>
        </div>

        <!-- Debug Test Buttons -->
        <div class="test-section">
            <h3>🐛 Debug API Responses</h3>
            <button onclick="debugCompanySearch('Equinor')">Debug Equinor Search</button>
            <button onclick="debugCompanyByOrgNumber('*********')">Debug Equinor Org#</button>
            <button onclick="debugKnownCompanies()">Debug All Known Companies</button>
            <p style="font-size: 12px; color: #6b7280; margin-top: 10px;">
                Check console for detailed API response analysis
            </p>
        </div>

        <!-- Console Output -->
        <div class="test-section">
            <h3>📋 Console Output</h3>
            <pre id="console" style="height: 200px; overflow-y: auto;"></pre>
            <button onclick="clearConsole()">Clear Console</button>
        </div>
    </div>

    <script type="module">
        // Import the company lookup functions
        import { searchCompanies, getCompanyByOrgNumber } from '/src/services/companyLookup.js';
        import { debugCompanySearch, debugCompanyByOrgNumber, debugKnownCompanies } from '/src/utils/debugCompanyAPI.js';

        // Console capture
        const consoleElement = document.getElementById('console');
        const originalLog = console.log;
        const originalError = console.error;

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString('no-NO');
            const color = type === 'error' ? '#dc2626' : type === 'warn' ? '#d97706' : '#16a34a';
            consoleElement.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span>\n`;
            consoleElement.scrollTop = consoleElement.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        // Test functions
        window.testSearch = async function() {
            const query = document.getElementById('searchInput').value;
            const resultsDiv = document.getElementById('searchResults');
            
            if (!query.trim()) {
                alert('Vennligst skriv inn et firmanavn');
                return;
            }

            resultsDiv.style.display = 'block';
            resultsDiv.className = 'results loading';
            resultsDiv.innerHTML = '🔄 Søker etter firma...';

            console.log(`🔍 Searching for: "${query}"`);

            try {
                const result = await searchCompanies(query, 10);

                if (result.success) {
                    console.log(`✅ Found ${result.data.companies.length} companies`);
                    
                    resultsDiv.className = 'results success';
                    resultsDiv.innerHTML = `
                        <strong>Fant ${result.data.companies.length} firma(er):</strong>
                        ${result.data.companies.map(company => `
                            <div class="company-card">
                                <div class="company-name">${company.name}</div>
                                <div class="company-details">
                                    Org.nr: ${company.organizationNumber}<br>
                                    ${(() => {
                                        const address = company.visitingAddress || company.businessAddress;
                                        if (address && (address.street || address.postalCode || address.city)) {
                                            const addressParts = [];
                                            if (address.street) addressParts.push(address.street);
                                            if (address.postalCode && address.city) addressParts.push(`${address.postalCode} ${address.city}`);
                                            return `Adresse: ${addressParts.join(', ')}`;
                                        }
                                        return 'Ingen adresse registrert';
                                    })()}<br>
                                    ${company.managingDirector ?
                                        `<span style="font-weight: 500;">Daglig leder:</span> ${company.managingDirector.fullName}` :
                                        '<span style="color: #6b7280;">Daglig leder ikke tilgjengelig</span>'
                                    }
                                </div>
                                <span class="status-badge status-${company.status}">
                                    ${company.status === 'active' ? 'Aktiv' : company.status === 'inactive' ? 'Inaktiv' : 'Oppløst'}
                                </span>
                            </div>
                        `).join('')}
                    `;
                } else {
                    console.error(`❌ Search failed: ${result.error.message}`);
                    resultsDiv.className = 'results error';
                    resultsDiv.innerHTML = `<strong>Feil:</strong> ${result.error.message}`;
                }
            } catch (error) {
                console.error(`💥 Unexpected error: ${error.message}`);
                resultsDiv.className = 'results error';
                resultsDiv.innerHTML = `<strong>Uventet feil:</strong> ${error.message}`;
            }
        };

        window.testOrgLookup = async function() {
            const orgNumber = document.getElementById('orgNumberInput').value;
            const resultsDiv = document.getElementById('orgResults');
            
            if (!orgNumber.trim()) {
                alert('Vennligst skriv inn et organisasjonsnummer');
                return;
            }

            resultsDiv.style.display = 'block';
            resultsDiv.className = 'results loading';
            resultsDiv.innerHTML = '🔄 Slår opp organisasjonsnummer...';

            console.log(`🔍 Looking up org number: "${orgNumber}"`);

            try {
                const result = await getCompanyByOrgNumber(orgNumber);

                if (result.success) {
                    console.log(`✅ Found company: ${result.data.name}`);
                    
                    const company = result.data;
                    resultsDiv.className = 'results success';
                    resultsDiv.innerHTML = `
                        <div class="company-card">
                            <div class="company-name">${company.name}</div>
                            <div class="company-details">
                                Org.nr: ${company.organizationNumber}<br>
                                ${company.visitingAddress ? 
                                    `Besøksadresse: ${company.visitingAddress.street}, ${company.visitingAddress.postalCode} ${company.visitingAddress.city}` : 
                                    'Ingen besøksadresse'
                                }<br>
                                ${company.businessAddress ? 
                                    `Forretningsadresse: ${company.businessAddress.street}, ${company.businessAddress.postalCode} ${company.businessAddress.city}` : 
                                    'Ingen forretningsadresse'
                                }<br>
                                ${company.industryDescription ? `Bransje: ${company.industryDescription}` : ''}
                            </div>
                            <span class="status-badge status-${company.status}">
                                ${company.status === 'active' ? 'Aktiv' : company.status === 'inactive' ? 'Inaktiv' : 'Oppløst'}
                            </span>
                        </div>
                    `;
                } else {
                    console.error(`❌ Lookup failed: ${result.error.message}`);
                    resultsDiv.className = 'results error';
                    resultsDiv.innerHTML = `<strong>Feil:</strong> ${result.error.message}`;
                }
            } catch (error) {
                console.error(`💥 Unexpected error: ${error.message}`);
                resultsDiv.className = 'results error';
                resultsDiv.innerHTML = `<strong>Uventet feil:</strong> ${error.message}`;
            }
        };

        window.quickTest = function(query) {
            document.getElementById('searchInput').value = query;
            testSearch();
        };

        window.quickOrgTest = function(orgNumber) {
            document.getElementById('orgNumberInput').value = orgNumber;
            testOrgLookup();
        };

        window.clearResults = function() {
            document.getElementById('searchResults').style.display = 'none';
            document.getElementById('searchInput').value = '';
        };

        window.clearOrgResults = function() {
            document.getElementById('orgResults').style.display = 'none';
            document.getElementById('orgNumberInput').value = '';
        };

        window.clearConsole = function() {
            consoleElement.innerHTML = '';
        };

        // Add Enter key support
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') testSearch();
        });

        document.getElementById('orgNumberInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') testOrgLookup();
        });

        console.log('🚀 Company Lookup Test Page Ready');
        console.log('💡 Try searching for Norwegian companies like "Equinor", "DNB", or "Telenor"');
    </script>
</body>
</html>
