# JobbLogg Chat-modul - Teknisk Dokumentasjon

## Oversikt

JobbLogg chat-modulen er et omfattende real-time kommunikasjonssystem som muliggjør samtaler mellom entreprenører og kunder knyttet til spesifikke prosjektlogg-oppføringer. Systemet støtter trådet samtaler, filvedlegg, emoji-reaksjoner, typing-indikatorer og omfattende tilgangskontroll.

## 1. Databasestruktur

### Messages Tabell

```typescript
interface Message {
  _id: Id<"messages">;
  logId: Id<"logEntries">;              // Kobling til prosjektlogg
  parentId?: Id<"messages">;            // Selvrelasjon for tråding
  senderId: string;                     // Bruker-ID fra Clerk
  senderRole: "customer" | "contractor"; // Rolle for tilgangskontroll
  text?: string;                        // Meldingstekst (påkrevd hvis ingen fil)
  file?: MessageFile;                   // Filvedlegg (påkrevd hvis ingen tekst)
  reactions?: MessageReaction[];        // Emoji-reaksjoner
  readBy?: Record<string, number>;      // Lesestatus per bruker
  deliveryStatus?: DeliveryStatus;      // Leveringsstatus
  deliveredTo?: Record<string, number>; // Leveringsbekreftelser
  createdAt: number;                    // Opprettelsestidspunkt (Unix epoch)
  updatedAt?: number;                   // Siste redigeringstidspunkt
  isEdited?: boolean;                   // Redigeringsstatus
  isDeleted?: boolean;                  // Soft delete-flagg
}

interface MessageFile {
  url: string;                          // Signert URL til fil
  name: string;                         // Opprinnelig filnavn
  size: number;                         // Filstørrelse i bytes
  type: string;                         // MIME-type
  thumbnailUrl?: string;                // Thumbnail for bilder/videoer
}

interface MessageReaction {
  emoji: string;                        // Emoji-karakter
  userIds: string[];                    // Bruker-IDer som reagerte
  count: number;                        // Antall reaksjoner (ytelse)
}

type DeliveryStatus = "sending" | "sent" | "delivered" | "failed";
```

### TypingIndicators Tabell

```typescript
interface TypingIndicator {
  _id: Id<"typingIndicators">;
  logId: Id<"logEntries">;              // Loggoppføring hvor bruker skriver
  userId: string;                       // Bruker som skriver
  userRole: "customer" | "contractor";  // Brukerrolle
  expiresAt: number;                    // Utløpstidspunkt (Unix epoch)
  createdAt: number;                    // Starttidspunkt
  updatedAt: number;                    // Siste aktivitetstidspunkt
}
```

### Indekser for Ytelse

```typescript
// Messages-tabellen
.index("by_log", ["logId"])                     // Alle meldinger for en logg
.index("by_parent", ["parentId"])               // Svar på en melding
.index("by_sender", ["senderId"])               // Meldinger fra en avsender
.index("by_log_and_created", ["logId", "createdAt"]) // Kronologisk sortering
.index("by_log_and_parent", ["logId", "parentId"])   // Trådstruktur

// TypingIndicators-tabellen
.index("by_log", ["logId"])                     // Typing-indikatorer for en logg
.index("by_log_and_user", ["logId", "userId"]) // Spesifikk brukers indikator
.index("by_expires", ["expiresAt"])             // Opprydding av utløpte indikatorer
```

## 2. Dataflyt mellom Frontend og Backend

### Meldingssending med Optimistic Updates

```mermaid
sequenceDiagram
    participant UI as React UI
    participant State as Local State
    participant Convex as Convex Backend
    participant DB as Database

    UI->>State: handleSendMessage()
    State->>State: Legg til optimistic message
    UI->>UI: Vis melding umiddelbart
    State->>Convex: sendMessage mutation
    Convex->>Convex: Valider tilgang og data
    Convex->>DB: Lagre melding
    DB->>Convex: Bekreft lagring
    Convex->>State: Returner meldings-ID
    State->>State: Fjern optimistic message
    Convex->>UI: Real-time oppdatering (subscription)
```

### Real-time Oppdateringer

```typescript
// Frontend subscription til meldinger
const messagesData = useQuery(api.messages.getMessagesWithDisplayNames, {
  logId,
  userId,
  userRole,
  limit: 50,
  cursor: undefined
});

// Automatisk re-rendering når nye meldinger ankommer
useEffect(() => {
  if (messagesData?.messages) {
    // Oppdater UI med nye meldinger
    setMessages(messagesData.messages);
  }
}, [messagesData]);
```

## 3. Komponenthierarki

### Hovedkomponenter

```
ChatContainer (Fullskjerm chat)
├── MessageList / VirtualizedMessageList
│   └── MessageItem
│       ├── MessageText / MessageTextWithPreviews
│       ├── FileAttachment
│       ├── EnhancedEmojiReactions
│       └── DeliveryStatus
├── TypingIndicator
├── MessageInput
│   ├── FilePreview
│   └── FileUploadHelp
└── ConnectionStatus

EmbeddedChatContainer (Innebygd i loggoppføringer)
├── MessageList (forenklet)
│   └── MessageItem (samme som over)
└── MessageInput (samme som over)
```

### Komponentansvar

**ChatContainer:**
- Hovedcontainer for fullskjerm chat
- Håndterer paginering og virtualisering
- Administrerer optimistic updates
- Keyboard navigation

**EmbeddedChatContainer:**
- Forenklet chat for loggoppføringer
- Alltid synlig design
- Begrenset høyde med scroll
- Samme funksjonalitet som ChatContainer

**MessageList:**
- Viser meldinger i trådet struktur
- Auto-scroll til bunn ved nye meldinger
- Kollapsible tråder
- Håndterer tom tilstand

**VirtualizedMessageList:**
- Ytelsesoptimalisert for store meldingslister
- Virtuell scrolling for >20 meldinger
- Redusert DOM-belastning

**MessageItem:**
- Individuell meldingsvisning
- Støtter tekst, filer og reaksjoner
- Hover-actions for redigering/sletting
- Leveringsstatus-indikatorer

**MessageInput:**
- Responsivt input-felt med auto-resize
- Filvedlegg med drag-and-drop
- Typing-indikatorer
- Keyboard shortcuts (Enter/Shift+Enter)

## 4. API-endepunkter

### Mutations (Dataendringer)

#### sendMessage
```typescript
api.messages.sendMessage({
  logId: Id<"logEntries">,
  parentId?: Id<"messages">,
  senderId: string,
  senderRole: "customer" | "contractor",
  text?: string,
  file?: MessageFile
})
```

**Validering:**
- Minst ett av `text` eller `file` må være definert
- Tilgangskontroll basert på rolle og prosjekteierskap
- Filtype og størrelse validering (10MB grense)

#### editMessage
```typescript
api.messages.editMessage({
  messageId: Id<"messages">,
  userId: string,
  text?: string,
  file?: MessageFile
})
```

**Begrensninger:**
- Kun eier kan redigere egne meldinger
- Slettede meldinger kan ikke redigeres
- Setter `isEdited: true` og `updatedAt`

#### deleteMessage
```typescript
api.messages.deleteMessage({
  messageId: Id<"messages">,
  userId: string
})
```

**Soft Delete:**
- Setter `isDeleted: true`
- Bevarer meldingsdata for historikk
- Filtreres ut fra queries

#### addReaction
```typescript
api.messages.addReaction({
  messageId: Id<"messages">,
  userId: string,
  emoji: string
})
```

**Funksjonalitet:**
- Legger til eller fjerner reaksjon (toggle)
- Oppdaterer `count` for ytelse
- Støtter kun én reaksjon per bruker per melding

#### markAsRead
```typescript
api.messages.markAsRead({
  logId: Id<"logEntries">,
  userId: string,
  userRole: "customer" | "contractor"
})
```

### Queries (Datahenting)

#### getMessagesWithDisplayNames
```typescript
api.messages.getMessagesWithDisplayNames({
  logId: Id<"logEntries">,
  userId: string,
  userRole: "customer" | "contractor",
  limit?: number,
  cursor?: number
})
```

**Returnerer:**
- Trådet meldingsstruktur
- Display-navn basert på rolle
- `isOwnMessage` flagg for styling
- Paginering med cursor-basert navigering
- **userNames mapping:** Record<string, string> for reaksjonsvisning med faktiske kundenavn

#### getUnreadCounts
```typescript
api.messages.getUnreadCounts({
  userId: string,
  userRole: "customer" | "contractor"
})
```

**Returnerer:**
- Totalt antall uleste meldinger
- Uleste per samtale/prosjekt
- Siste aktivitetstidspunkt

#### getTypingIndicators
```typescript
api.messages.getTypingIndicators({
  logId: Id<"logEntries">,
  userId: string
})
```

**Real-time:**
- Filtrerer ut egen bruker
- Fjerner utløpte indikatorer
- Returnerer display-navn basert på rolle

### Filhåndtering

#### generateChatUploadUrl
```typescript
api.messages.generateChatUploadUrl()
```

#### storeChatFile
```typescript
api.messages.storeChatFile({
  storageId: Id<"_storage">,
  fileName: string,
  fileType: string,
  fileSize: number,
  userId: string
})
```

**Støttede filtyper:**
- Bilder: JPEG, PNG, WebP
- Dokumenter: PDF, Word, TXT
- Regneark: Excel

**Validering:**
- Maksimal størrelse: 10MB
- MIME-type validering
- Automatisk thumbnail for bilder

## 5. Brukergrensesnitt og UX-mønstre

### Meldingsbobler

```typescript
// Styling basert på eierskap
const bubbleClasses = isOwnMessage
  ? "bg-jobblogg-primary text-white ml-auto"
  : "bg-jobblogg-card text-jobblogg-text-strong mr-auto";

// Responsive design
<div className={`
  max-w-[85%] sm:max-w-[70%]
  rounded-2xl px-4 py-3
  ${bubbleClasses}
  ${isOwnMessage ? 'rounded-br-md' : 'rounded-bl-md'}
`}>
```

**Design-prinsipper:**
- **Egne meldinger:** Blå bakgrunn, høyrejustert
- **Andres meldinger:** Grå bakgrunn, venstrejustert
- **Responsive bredde:** 85% på mobil, 70% på desktop
- **Avrundede hjørner:** Mindre radius på "hale"-siden

### Reaksjoner (Enhanced Emoji System)

```typescript
// Unified reaction display (Facebook-stil)
<div className="flex items-center gap-1 mt-2">
  {/* Overlappende emoji-ikoner */}
  <div className="flex -space-x-1">
    {uniqueEmojis.map(emoji => (
      <span key={emoji} className="text-sm bg-white rounded-full p-1 border">
        {emoji}
      </span>
    ))}
  </div>

  {/* Total count */}
  <span className="text-xs text-jobblogg-text-muted ml-1">
    {totalReactions}
  </span>

  {/* Action button */}
  <button
    onClick={handleReactionClick}
    className="text-xs text-jobblogg-text-muted hover:text-jobblogg-primary"
  >
    {userHasReacted ? 'Fjern' : 'Reager'}
  </button>
</div>
```

**Funksjonalitet:**
- **Single reaction per user:** Ny reaksjon erstatter eksisterende
- **Unified display:** Horizontal bar med overlappende emojis
- **Tooltip på hover:** Viser hvem som reagerte med faktiske kundenavn
- **Optimistic updates:** Umiddelbar UI-respons
- **Customer name display:** Viser faktiske kundenavn i stedet for "anonym" eller generiske etiketter

### Customer Name Display i Reaksjoner

**Problem løst (2024-12):** Chat-reaksjoner viste tidligere "anonym" eller generiske etiketter i stedet for faktiske kundenavn.

**Løsning implementert:**

```typescript
// Backend: getMessagesWithDisplayNames returnerer nå userNames mapping
export const getMessagesWithDisplayNames = query({
  // ... args
  handler: async (ctx, args) => {
    // Samle alle bruker-IDer fra meldinger og reaksjoner
    const userNames: Record<string, string> = {};

    // Map kundesession-IDer til faktiske kundenavn
    customerSessionIds.forEach(sessionId => {
      userNames[sessionId] = getDisplayName("customer", customer);
    });

    return {
      messages: messagesWithDisplayNames,
      userNames // Ny: Mapping av bruker-IDer til visningsnavn
    };
  }
});

// Frontend: EnhancedEmojiReactions bruker userNames mapping
const getAllReactionsTooltip = () => {
  // Viser faktiske kundenavn i stedet for "1 reaksjoner"
  if (reactions.length === 1) {
    return getTooltipText(reactions[0]); // "Ola Nordmann reagerte med 👍"
  }
  // Håndterer flere reaksjonstyper med kundenavn
};
```

**Resultat:**
- ✅ **Før:** "1 reaksjoner" eller "anonym reagerte med 👍"
- ✅ **Etter:** "Ola Nordmann reagerte med 👍"
- ✅ **Konsistent:** Viser kundenavn uavhengig av om entreprenør også har reagert

### Lest-status Indikatorer

```typescript
// Leveringsstatus under meldinger
const getDeliveryStatusText = (message: Message) => {
  if (message.deliveryStatus === "failed") {
    return "Sending feilet";
  }

  if (message.readBy && Object.keys(message.readBy).length > 0) {
    const readTimestamp = Object.values(message.readBy)[0];
    return `Lest ${formatTimestamp(readTimestamp)}`;
  }

  if (message.deliveryStatus === "delivered") {
    return "Levert";
  }

  return "Sendt";
};

// Visuell indikator
<div className="text-xs text-jobblogg-text-muted mt-1">
  {isOwnMessage && (
    <span className="flex items-center gap-1">
      <DeliveryIcon status={message.deliveryStatus} />
      {getDeliveryStatusText(message)}
    </span>
  )}
</div>
```

**Statusnivåer:**
1. **Sending:** Spinner-ikon
2. **Sent:** Enkelt hake-ikon
3. **Delivered:** Dobbelt hake-ikon
4. **Read:** "Lest DD.MM.YY, HH:MM" format
5. **Failed:** Rød feil-ikon med retry-knapp

### Typing-indikatorer

```typescript
// Real-time typing display
const TypingIndicator: React.FC = ({ typingUsers }) => {
  if (typingUsers.length === 0) return null;

  return (
    <div className="flex items-center gap-2 px-4 py-2 text-jobblogg-text-muted">
      <div className="flex space-x-1">
        {[1, 2, 3].map(i => (
          <div
            key={i}
            className="w-2 h-2 bg-jobblogg-text-muted rounded-full animate-bounce"
            style={{ animationDelay: `${i * 0.1}s` }}
          />
        ))}
      </div>
      <span className="text-sm">
        {typingUsers.length === 1
          ? `${typingUsers[0].displayName} skriver...`
          : `${typingUsers.length} personer skriver...`
        }
      </span>
    </div>
  );
};
```

## 6. Håndtering av Filer og Bilder

### Filvedlegg i Chat

```typescript
// FileAttachment component
const FileAttachment: React.FC<{ file: MessageFile }> = ({ file }) => {
  const isImage = file.type.startsWith('image/');

  if (isImage) {
    return (
      <div className="relative group">
        <img
          src={file.thumbnailUrl || file.url}
          alt={file.name}
          className="max-w-xs rounded-lg cursor-pointer hover:opacity-90"
          onClick={() => openImageModal(file.url)}
        />
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-lg transition-all" />
      </div>
    );
  }

  return (
    <div className="flex items-center gap-3 p-3 bg-jobblogg-card rounded-lg border">
      <FileIcon type={file.type} />
      <div className="flex-1 min-w-0">
        <p className="font-medium text-jobblogg-text-strong truncate">
          {file.name}
        </p>
        <p className="text-sm text-jobblogg-text-muted">
          {formatFileSize(file.size)}
        </p>
      </div>
      <a
        href={file.url}
        download={file.name}
        className="text-jobblogg-primary hover:text-jobblogg-primary-dark"
      >
        Last ned
      </a>
    </div>
  );
};
```

### Drag-and-Drop Filvedlegg

```typescript
// MessageInput med drag-and-drop
const MessageInput: React.FC = () => {
  const [isDragging, setIsDragging] = useState(false);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, []);

  return (
    <div
      className={`relative ${isDragging ? 'bg-jobblogg-primary-light' : ''}`}
      onDragOver={(e) => { e.preventDefault(); setIsDragging(true); }}
      onDragLeave={() => setIsDragging(false)}
      onDrop={handleDrop}
    >
      {isDragging && (
        <div className="absolute inset-0 border-2 border-dashed border-jobblogg-primary bg-jobblogg-primary-light bg-opacity-50 flex items-center justify-center z-10">
          <p className="text-jobblogg-primary font-medium">
            Slipp filen her for å laste opp
          </p>
        </div>
      )}

      {/* Input fields */}
    </div>
  );
};
```

### Bildehåndtering og Thumbnails

```typescript
// Automatisk thumbnail-generering
const generateThumbnail = async (file: File): Promise<string> => {
  if (!file.type.startsWith('image/')) return '';

  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Resize til maksimalt 300x300 med aspect ratio
      const maxSize = 300;
      const ratio = Math.min(maxSize / img.width, maxSize / img.height);

      canvas.width = img.width * ratio;
      canvas.height = img.height * ratio;

      ctx?.drawImage(img, 0, 0, canvas.width, canvas.height);
      resolve(canvas.toDataURL('image/jpeg', 0.8));
    };

    img.src = URL.createObjectURL(file);
  });
};
```

## 7. Sikkerhet og Tilgangskontroll

### Rolle-basert Tilgang

```typescript
// Tilgangskontroll i sendMessage
const validateAccess = async (ctx, args) => {
  const logEntry = await ctx.db.get(args.logId);
  const project = await ctx.db.get(logEntry.projectId);

  if (args.senderRole === "contractor") {
    // Entreprenør må eie prosjektet
    if (project.userId !== args.senderId) {
      throw new Error("Du har ikke tilgang til å sende meldinger i dette prosjektet");
    }
  } else if (args.senderRole === "customer") {
    // Kunde må ha tilgang via delt prosjekt
    if (!project.isPubliclyShared) {
      throw new Error("Dette prosjektet er ikke delt offentlig");
    }
  }
};
```

### Datavalidering

```typescript
// Input-validering og sanitering
const validateMessageInput = (args) => {
  // Krev minst ett av tekst eller fil
  if (!args.text && !args.file) {
    throw new Error("Melding må inneholde tekst eller fil");
  }

  // Valider tekst-lengde
  if (args.text && args.text.length > 5000) {
    throw new Error("Meldingstekst kan ikke være lengre enn 5000 tegn");
  }

  // Valider filtype og størrelse
  if (args.file) {
    validateFileUpload(args.file);
  }
};

const validateFileUpload = (file) => {
  const allowedTypes = [
    'image/jpeg', 'image/png', 'image/webp',
    'application/pdf', 'text/plain'
  ];

  if (!allowedTypes.includes(file.type)) {
    throw new Error(`Filtypen '${file.type}' støttes ikke`);
  }

  if (file.size > 10 * 1024 * 1024) { // 10MB
    throw new Error("Filen er for stor. Maksimal størrelse er 10MB");
  }
};
```

### Autentisering med Clerk

```typescript
// Frontend autentisering
const ChatContainer: React.FC = () => {
  const { user, isLoaded } = useUser();

  if (!isLoaded) return <LoadingSpinner />;
  if (!user) return <Navigate to="/sign-in" />;

  return (
    <div className="chat-container">
      {/* Chat innhold */}
    </div>
  );
};

// Backend autentisering (automatisk via Convex)
export const sendMessage = mutation({
  args: { /* ... */ },
  handler: async (ctx, args) => {
    // ctx.auth inneholder automatisk autentisert bruker fra Clerk
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    // Valider at senderId matcher autentisert bruker
    if (args.senderId !== identity.subject) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Fortsett med meldingshåndtering...
  }
});
```

## 8. Ytelsesoptimalisering og Caching-strategier

### Virtuell Scrolling

```typescript
// VirtualizedMessageList for store samtaler
const VirtualizedMessageList: React.FC = ({ messages }) => {
  const { size, elementRef } = useElementSize();

  // Aktiver virtualisering kun for store lister
  const enableVirtualization = messages.length > 20 && size.height > 300;

  const virtualScroll = useVirtualScroll(flattenedMessages, {
    itemHeight: 120, // Estimert meldingshøyde
    containerHeight: size.height || 400,
    overscan: 3 // Render 3 ekstra elementer utenfor synlig område
  });

  if (!enableVirtualization) {
    // Vanlig rendering for små lister
    return <MessageList messages={messages} />;
  }

  return (
    <div
      ref={virtualScroll.scrollElementRef}
      className="h-full overflow-y-auto"
      onScroll={virtualScroll.handleScroll}
    >
      <div style={{ height: virtualScroll.totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${virtualScroll.offsetY}px)` }}>
          {flattenedMessages
            .slice(virtualScroll.startIndex, virtualScroll.endIndex + 1)
            .map((item, index) => (
              <MessageItem
                key={item.message._id}
                message={item.message}
                level={item.level}
                // ... andre props
              />
            ))
          }
        </div>
      </div>
    </div>
  );
};
```

### Optimistic Updates

```typescript
// Umiddelbar UI-respons før server-bekreftelse
const handleSendMessage = useCallback(async (data: MessageFormData) => {
  const optimisticId = `optimistic-${Date.now()}-${Math.random()}`;

  // 1. Legg til optimistic message umiddelbart
  const optimisticMessage: OptimisticMessage = {
    _id: optimisticId,
    logId,
    senderId: userId,
    senderRole: userRole,
    text: data.text,
    file: data.file,
    createdAt: Date.now(),
    isOptimistic: true,
    isSending: true
  };

  setOptimisticMessages(prev => [...prev, optimisticMessage]);

  try {
    // 2. Send til server
    await sendMessage(data);

    // 3. Fjern optimistic message når server bekrefter
    setOptimisticMessages(prev =>
      prev.filter(msg => msg._id !== optimisticId)
    );
  } catch (error) {
    // 4. Marker som feilet hvis sending mislykkes
    setOptimisticMessages(prev =>
      prev.map(msg =>
        msg._id === optimisticId
          ? { ...msg, isSending: false, sendError: error.message }
          : msg
      )
    );
  }
}, [sendMessage, logId, userId, userRole]);
```

### Paginering og Lazy Loading

```typescript
// Cursor-basert paginering for store samtaler
const useChatPagination = (logId: string) => {
  const [messageLimit, setMessageLimit] = useState(50);
  const [messageCursor, setMessageCursor] = useState<number | undefined>();
  const [hasMore, setHasMore] = useState(true);

  const messagesData = useQuery(api.messages.getMessagesWithDisplayNames, {
    logId,
    limit: messageLimit,
    cursor: messageCursor
  });

  const loadMore = useCallback(() => {
    if (messagesData?.messages && messagesData.messages.length > 0) {
      const oldestMessage = messagesData.messages[messagesData.messages.length - 1];
      setMessageCursor(oldestMessage.createdAt);
      setMessageLimit(prev => prev + 25);
    }
  }, [messagesData]);

  // Intersection Observer for automatisk lasting
  const loadMoreRef = useIntersectionObserver(
    (entries) => {
      if (entries[0].isIntersecting && hasMore) {
        loadMore();
      }
    },
    { threshold: 0.1 }
  );

  return { messagesData, loadMore, loadMoreRef, hasMore };
};
```

### Caching og Memoization

```typescript
// Memoized message processing
const processedMessages = useMemo(() => {
  if (!messagesData?.messages) return [];

  return messagesData.messages.map(message => ({
    ...message,
    formattedTime: formatTimestamp(message.createdAt),
    hasReactions: message.reactions && message.reactions.length > 0,
    reactionSummary: summarizeReactions(message.reactions),
    isRecent: Date.now() - message.createdAt < 5 * 60 * 1000 // 5 minutter
  }));
}, [messagesData?.messages]);

// Debounced typing indicators
const debouncedStartTyping = useMemo(
  () => debounce(() => {
    startTyping({ logId, userId, userRole });
  }, 300),
  [startTyping, logId, userId, userRole]
);

const debouncedStopTyping = useMemo(
  () => debounce(() => {
    stopTyping({ logId, userId });
  }, 1000),
  [stopTyping, logId, userId]
);
```

### Real-time Optimalisering

```typescript
// Conditional queries for bedre ytelse
const messagesData = useQuery(
  api.messages.getMessagesWithDisplayNames,
  isVisible ? { logId, userId, userRole } : "skip"
);

// Throttled scroll events
const throttledScrollHandler = useCallback(
  throttle((event: React.UIEvent<HTMLDivElement>) => {
    const element = event.currentTarget;
    const isAtBottom = element.scrollHeight - element.scrollTop <= element.clientHeight + 50;

    if (isAtBottom && hasUnreadMessages) {
      markAsRead({ logId, userId, userRole });
    }
  }, 500),
  [markAsRead, logId, userId, userRole, hasUnreadMessages]
);
```

## 9. Testing og Debugging

### Enhetstesting

```typescript
// MessageItem.test.tsx
describe('MessageItem', () => {
  it('should render own message with correct styling', () => {
    render(
      <MessageItem
        message={mockOwnMessage}
        userId="user123"
        isOwnMessage={true}
      />
    );

    expect(screen.getByText(mockOwnMessage.text)).toBeInTheDocument();
    expect(screen.getByTestId('message-bubble')).toHaveClass('bg-jobblogg-primary');
  });

  it('should handle file attachments correctly', () => {
    const messageWithFile = {
      ...mockMessage,
      file: { url: 'test.pdf', name: 'document.pdf', type: 'application/pdf' }
    };

    render(<MessageItem message={messageWithFile} userId="user123" />);
    expect(screen.getByText('document.pdf')).toBeInTheDocument();
  });
});
```

### Performance Monitoring

```typescript
// Performance metrics for chat
const useChatPerformance = () => {
  const [metrics, setMetrics] = useState({
    messageRenderTime: 0,
    scrollPerformance: 0,
    typingLatency: 0
  });

  const measureRenderTime = useCallback(() => {
    const start = performance.now();

    // Measure after next render
    requestAnimationFrame(() => {
      const end = performance.now();
      setMetrics(prev => ({
        ...prev,
        messageRenderTime: end - start
      }));
    });
  }, []);

  return { metrics, measureRenderTime };
};
```

## 10. Fremtidige Forbedringer

### Planlagte Funksjoner

1. **Voice Messages:** Lydopptak og avspilling
2. **Message Search:** Søk i chat-historikk
3. **Message Threading:** Forbedret tråd-navigering
4. **Push Notifications:** Real-time varsler
5. **Message Encryption:** End-to-end kryptering
6. **Offline Support:** Sync når tilkobling gjenopprettes

### Arkitektur-forbedringer

1. **WebSocket Integration:** Redusert latency for real-time updates
2. **Message Batching:** Gruppering av meldinger for bedre ytelse
3. **Advanced Caching:** Redis for session-data
4. **CDN Integration:** Raskere filnedlasting
5. **Analytics Integration:** Brukerengasjement-metrics

---

## Konklusjon

JobbLogg chat-modulen er et robust, skalerbart kommunikasjonssystem som balanserer funksjonalitet med ytelse. Systemet støtter komplekse brukerinteraksjoner samtidig som det opprettholder høy sikkerhet og tilgjengelighet.

**Nøkkelfunksjoner:**
- ✅ Real-time kommunikasjon med Convex
- ✅ Rolle-basert tilgangskontroll
- ✅ Filvedlegg og bildehåndtering
- ✅ Emoji-reaksjoner og interaktivitet
- ✅ Optimistic updates for responsivitet
- ✅ Virtuell scrolling for ytelse
- ✅ Comprehensive error handling
- ✅ Mobile-first responsive design

Systemet er designet for å skalere med JobbLogg-plattformens vekst og kan enkelt utvides med nye funksjoner uten å påvirke eksisterende funksjonalitet.
```
