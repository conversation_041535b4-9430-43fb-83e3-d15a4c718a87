/**
 * Investigation script for managing director data in Brønnøysundregisteret API
 */

async function investigateManagerData() {
  console.log('🔍 Investigating managing director data in Brønnøysundregisteret API...');
  
  try {
    // Test with Equinor to see full API response structure
    console.log('\n--- Testing main enheter API ---');
    const response = await fetch('https://data.brreg.no/enhetsregisteret/api/enheter/*********', {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'JobbLogg/1.0 (Manager Data Investigation)'
      }
    });

    if (!response.ok) {
      console.error('❌ API Error:', response.status);
      return;
    }

    const data = await response.json();
    
    console.log('📊 Available fields in main API:');
    console.log(Object.keys(data).sort());
    
    // Look for manager-related fields
    const managerFields = Object.keys(data).filter(key => 
      key.toLowerCase().includes('leder') || 
      key.toLowerCase().includes('manager') || 
      key.toLowerCase().includes('daglig') ||
      key.toLowerCase().includes('styrer') ||
      key.toLowerCase().includes('kontakt') ||
      key.toLowerCase().includes('person') ||
      key.toLowerCase().includes('rolle')
    );
    
    console.log('\n🎯 Manager-related fields found:', managerFields);
    
    // Check specific fields that might contain useful info
    console.log('\n📋 Key field analysis:');
    console.log('navn:', data.navn);
    console.log('organisasjonsform:', data.organisasjonsform);
    
    // Test if there are separate APIs for roles/persons
    console.log('\n--- Testing roles API ---');
    try {
      const rolesResponse = await fetch('https://data.brreg.no/enhetsregisteret/api/enheter/*********/roller', {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'JobbLogg/1.0 (Roles Investigation)'
        }
      });
      
      if (rolesResponse.ok) {
        const rolesData = await rolesResponse.json();
        console.log('✅ Roles API response:', JSON.stringify(rolesData, null, 2));
      } else {
        console.log('❌ Roles API status:', rolesResponse.status);
      }
    } catch (error) {
      console.log('❌ Roles API error:', error.message);
    }
    
    // Check if there's a different API for person data
    console.log('\n--- Testing alternative APIs ---');
    
    // Try underenheter (sub-units) API
    try {
      const underResponse = await fetch('https://data.brreg.no/enhetsregisteret/api/underenheter?overordnetEnhet=*********&size=1', {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'JobbLogg/1.0 (Sub-units Investigation)'
        }
      });
      
      if (underResponse.ok) {
        const underData = await underResponse.json();
        console.log('📊 Sub-units API available, sample response keys:', Object.keys(underData));
      } else {
        console.log('❌ Sub-units API status:', underResponse.status);
      }
    } catch (error) {
      console.log('❌ Sub-units API error:', error.message);
    }
    
    // Research the API documentation structure
    console.log('\n--- API Documentation Research ---');
    console.log('Based on Norwegian business register structure:');
    console.log('- Main API (enheter): Company basic info, addresses, industry codes');
    console.log('- Roles/Persons: Typically separate API or not publicly available');
    console.log('- Managing director info: Often protected personal data');
    
    console.log('\n🎯 Conclusion:');
    console.log('The main enheter API does not appear to include managing director personal information.');
    console.log('This is likely due to privacy regulations (GDPR) protecting personal data.');
    console.log('We should implement the Daglig Leder field as a manual input field.');
    
  } catch (error) {
    console.error('💥 Error:', error.message);
  }
}

// Test with multiple companies
async function testMultipleCompanies() {
  const companies = [
    { name: 'Equinor ASA', orgNumber: '*********' },
    { name: 'DNB Bank ASA', orgNumber: '*********' },
    { name: 'Telenor ASA', orgNumber: '*********' }
  ];
  
  console.log('\n🧪 Testing multiple companies for manager data...');
  
  for (const company of companies) {
    console.log(`\n--- ${company.name} (${company.orgNumber}) ---`);
    
    try {
      const response = await fetch(`https://data.brreg.no/enhetsregisteret/api/enheter/${company.orgNumber}`, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'JobbLogg/1.0 (Multi-company Test)'
        }
      });

      if (response.ok) {
        const data = await response.json();
        
        // Look for any fields that might contain person/manager info
        const relevantFields = {};
        Object.keys(data).forEach(key => {
          if (key.toLowerCase().includes('leder') || 
              key.toLowerCase().includes('person') || 
              key.toLowerCase().includes('kontakt') ||
              key.toLowerCase().includes('rolle') ||
              key.toLowerCase().includes('styrer') ||
              key.toLowerCase().includes('daglig')) {
            relevantFields[key] = data[key];
          }
        });
        
        if (Object.keys(relevantFields).length > 0) {
          console.log('📋 Relevant fields found:', relevantFields);
        } else {
          console.log('❌ No manager/person fields found');
        }
        
      } else {
        console.log('❌ API Error:', response.status);
      }
      
    } catch (error) {
      console.log('💥 Error:', error.message);
    }
    
    // Small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

async function runInvestigation() {
  await investigateManagerData();
  await testMultipleCompanies();
}

runInvestigation();
