/**
 * Test script to verify managing director API integration
 */

async function testManagingDirectorIntegration() {
  console.log('🧪 Testing Managing Director API Integration...');
  
  const testCompanies = [
    { name: 'Equinor ASA', orgNumber: '*********', expectedCEO: '<PERSON>' },
    { name: 'DNB Bank ASA', orgNumber: '*********' },
    { name: 'Telenor ASA', orgNumber: '*********' }
  ];
  
  for (const testCompany of testCompanies) {
    console.log(`\n--- Testing ${testCompany.name} (${testCompany.orgNumber}) ---`);
    
    try {
      // Test the roles API directly
      const rolesResponse = await fetch(`https://data.brreg.no/enhetsregisteret/api/enheter/${testCompany.orgNumber}/roller`, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'JobbLogg/1.0 (Managing Director Test)'
        }
      });

      if (!rolesResponse.ok) {
        console.log(`❌ Roles API Error: ${rolesResponse.status}`);
        continue;
      }

      const rolesData = await rolesResponse.json();
      
      // Look for managing director
      const managingDirectorGroup = rolesData.rollegrupper?.find(group => 
        group.type?.kode === 'DAGL'
      );
      
      if (managingDirectorGroup?.roller?.[0]?.person) {
        const person = managingDirectorGroup.roller[0].person;
        const navn = person.navn;
        const fullName = [navn.fornavn, navn.mellomnavn, navn.etternavn].filter(Boolean).join(' ');
        
        console.log(`✅ Managing Director found: ${fullName}`);
        
        if (testCompany.expectedCEO && fullName.includes(testCompany.expectedCEO)) {
          console.log(`✅ Expected CEO confirmed: ${testCompany.expectedCEO}`);
        }
        
        // Test our mapping logic
        const managingDirector = {
          firstName: navn.fornavn || '',
          lastName: navn.etternavn || '',
          fullName: fullName,
          birthDate: person.fodselsdato
        };
        
        console.log(`📋 Mapped data:`, managingDirector);
        
      } else {
        console.log(`❌ No managing director found`);
      }
      
    } catch (error) {
      console.error(`💥 Error testing ${testCompany.name}:`, error.message);
    }
    
    // Small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

async function testCompanyLookupWithManager() {
  console.log('\n🔍 Testing Company Lookup with Managing Director...');
  
  try {
    // Simulate our company lookup service
    const response = await fetch('https://data.brreg.no/enhetsregisteret/api/enheter/*********', {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'JobbLogg/1.0 (Company Lookup Test)'
      }
    });

    if (!response.ok) {
      console.error('❌ Company API Error:', response.status);
      return;
    }

    const enhet = await response.json();
    
    // Fetch managing director in parallel
    const rolesResponse = await fetch(`https://data.brreg.no/enhetsregisteret/api/enheter/*********/roller`, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'JobbLogg/1.0 (Roles Test)'
      }
    });

    let managingDirector = null;
    
    if (rolesResponse.ok) {
      const rolesData = await rolesResponse.json();
      const managingDirectorGroup = rolesData.rollegrupper?.find(group => 
        group.type?.kode === 'DAGL'
      );
      
      if (managingDirectorGroup?.roller?.[0]?.person) {
        const person = managingDirectorGroup.roller[0].person;
        const navn = person.navn;
        
        managingDirector = {
          firstName: navn.fornavn || '',
          lastName: navn.etternavn || '',
          fullName: [navn.fornavn, navn.mellomnavn, navn.etternavn].filter(Boolean).join(' '),
          birthDate: person.fodselsdato
        };
      }
    }
    
    // Simulate our CompanyInfo object
    const company = {
      name: enhet.navn,
      organizationNumber: enhet.organisasjonsnummer,
      managingDirector: managingDirector,
      visitingAddress: null,
      businessAddress: null,
      status: 'active'
    };
    
    // Add address info
    if (enhet.forretningsadresse) {
      const addr = enhet.forretningsadresse;
      company.businessAddress = {
        street: [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim(),
        postalCode: addr.postnummer || '',
        city: addr.poststed || ''
      };
    }
    
    console.log('📊 Complete Company Object:');
    console.log('Name:', company.name);
    console.log('Org Number:', company.organizationNumber);
    console.log('Managing Director:', company.managingDirector);
    console.log('Address:', company.businessAddress);
    
    // Test auto-fill simulation
    console.log('\n🎯 Auto-fill Simulation:');
    const autoFillData = {
      customerName: company.name,
      orgNumber: company.organizationNumber,
      managingDirector: company.managingDirector?.fullName || '',
      address: company.businessAddress ? 
        `${company.businessAddress.street}, ${company.businessAddress.postalCode} ${company.businessAddress.city}` : 
        ''
    };
    
    console.log('Auto-fill data:', autoFillData);
    
  } catch (error) {
    console.error('💥 Error in company lookup test:', error.message);
  }
}

async function runTests() {
  await testManagingDirectorIntegration();
  await testCompanyLookupWithManager();
  
  console.log('\n✅ Managing Director Integration Tests Complete');
}

runTests();
