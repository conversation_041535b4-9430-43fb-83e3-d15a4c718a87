/**
 * Node.js script to test the Brønnøysundregisteret API and analyze address fields
 * Run with: node scripts/test-api-response.js
 */

async function testEquinorAPI() {
  console.log('🔍 Testing Equinor API response...');
  
  try {
    // Test Equinor by organization number
    const response = await fetch('https://data.brreg.no/enhetsregisteret/api/enheter/*********', {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'JobbLogg/1.0 (Test Script)'
      }
    });

    if (!response.ok) {
      console.error(`❌ API Error: ${response.status} ${response.statusText}`);
      return;
    }

    const enhet = await response.json();
    
    console.log('\n📊 Raw API Response for Equinor:');
    console.log('Company:', enhet.navn);
    console.log('Org.nr:', enhet.organisasjonsnummer);
    
    console.log('\n📍 Address Fields:');
    console.log('beliggenhetsadresse:', JSON.stringify(enhet.beliggenhetsadresse, null, 2));
    console.log('forretningsadresse:', JSON.stringify(enhet.forretningsadresse, null, 2));
    
    // Test our mapping logic
    if (enhet.beliggenhetsadresse) {
      const addr = enhet.beliggenhetsadresse;
      const mappedStreet = [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim();
      
      console.log('\n🔄 Our Address Mapping:');
      console.log('Street:', `"${mappedStreet}"`);
      console.log('Postal Code:', `"${addr.postnummer || ''}"`);
      console.log('City:', `"${addr.poststed || ''}"`);
      console.log('Street is empty?', mappedStreet === '');
    }
    
  } catch (error) {
    console.error('💥 Error:', error.message);
  }
}

async function testDNBAPI() {
  console.log('\n🔍 Testing DNB API response...');
  
  try {
    // Test DNB by organization number
    const response = await fetch('https://data.brreg.no/enhetsregisteret/api/enheter/*********', {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'JobbLogg/1.0 (Test Script)'
      }
    });

    if (!response.ok) {
      console.error(`❌ API Error: ${response.status} ${response.statusText}`);
      return;
    }

    const enhet = await response.json();
    
    console.log('\n📊 Raw API Response for DNB:');
    console.log('Company:', enhet.navn);
    console.log('Org.nr:', enhet.organisasjonsnummer);
    
    console.log('\n📍 Address Fields:');
    console.log('beliggenhetsadresse:', JSON.stringify(enhet.beliggenhetsadresse, null, 2));
    console.log('forretningsadresse:', JSON.stringify(enhet.forretningsadresse, null, 2));
    
    // Test our mapping logic
    if (enhet.beliggenhetsadresse) {
      const addr = enhet.beliggenhetsadresse;
      const mappedStreet = [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim();
      
      console.log('\n🔄 Our Address Mapping:');
      console.log('Street:', `"${mappedStreet}"`);
      console.log('Postal Code:', `"${addr.postnummer || ''}"`);
      console.log('City:', `"${addr.poststed || ''}"`);
      console.log('Street is empty?', mappedStreet === '');
    }
    
  } catch (error) {
    console.error('💥 Error:', error.message);
  }
}

async function testSearchAPI() {
  console.log('\n🔍 Testing search API for "Equinor"...');
  
  try {
    const url = new URL('https://data.brreg.no/enhetsregisteret/api/enheter');
    url.searchParams.set('navn', 'Equinor');
    url.searchParams.set('size', '3');
    
    const response = await fetch(url.toString(), {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'JobbLogg/1.0 (Test Script)'
      }
    });

    if (!response.ok) {
      console.error(`❌ API Error: ${response.status} ${response.statusText}`);
      return;
    }

    const data = await response.json();
    
    console.log(`\n📊 Search Results (${data._embedded?.enheter?.length || 0} companies):`);
    
    if (data._embedded?.enheter) {
      data._embedded.enheter.forEach((enhet, index) => {
        console.log(`\n--- Company ${index + 1}: ${enhet.navn} ---`);
        console.log('Org.nr:', enhet.organisasjonsnummer);
        
        if (enhet.beliggenhetsadresse) {
          const addr = enhet.beliggenhetsadresse;
          const mappedStreet = [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim();
          console.log('Address mapping:');
          console.log('  Street:', `"${mappedStreet}"`);
          console.log('  Postal:', `"${addr.postnummer || ''}"`);
          console.log('  City:', `"${addr.poststed || ''}"`);
          console.log('  Empty street?', mappedStreet === '');
        } else {
          console.log('❌ No beliggenhetsadresse');
        }
      });
    }
    
  } catch (error) {
    console.error('💥 Error:', error.message);
  }
}

// Run tests
async function runTests() {
  await testEquinorAPI();
  await testDNBAPI();
  await testSearchAPI();
}

runTests();
