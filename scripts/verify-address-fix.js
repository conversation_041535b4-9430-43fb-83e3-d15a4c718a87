/**
 * Verification script to test the address fix
 * Run with: node scripts/verify-address-fix.js
 */

async function testAddressFix() {
  console.log('🔍 Testing address fix with known companies...');
  
  const testCompanies = [
    { name: 'Equinor ASA', orgNumber: '*********' },
    { name: 'DNB Bank ASA', orgNumber: '*********' },
    { name: 'Telenor ASA', orgNumber: '*********' }
  ];
  
  for (const testCompany of testCompanies) {
    console.log(`\n--- Testing ${testCompany.name} (${testCompany.orgNumber}) ---`);
    
    try {
      const response = await fetch(`https://data.brreg.no/enhetsregisteret/api/enheter/${testCompany.orgNumber}`, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'JobbLogg/1.0 (Address Fix Test)'
        }
      });

      if (!response.ok) {
        console.log(`❌ API Error: ${response.status}`);
        continue;
      }

      const enhet = await response.json();
      
      // Simulate our mapping logic
      const company = {
        name: enhet.navn,
        organizationNumber: enhet.organisasjonsnummer,
        visitingAddress: null,
        businessAddress: null
      };

      // Map visiting address
      if (enhet.beliggenhetsadresse) {
        const addr = enhet.beliggenhetsadresse;
        company.visitingAddress = {
          street: [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim(),
          postalCode: addr.postnummer || '',
          city: addr.poststed || '',
          municipality: addr.kommune
        };
      }

      // Map business address
      if (enhet.forretningsadresse) {
        const addr = enhet.forretningsadresse;
        company.businessAddress = {
          street: [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim(),
          postalCode: addr.postnummer || '',
          city: addr.poststed || '',
          municipality: addr.kommune
        };
      }

      // Test our new display logic
      const address = company.visitingAddress || company.businessAddress;
      
      console.log('Raw data:');
      console.log('  beliggenhetsadresse:', enhet.beliggenhetsadresse ? 'EXISTS' : 'MISSING');
      console.log('  forretningsadresse:', enhet.forretningsadresse ? 'EXISTS' : 'MISSING');
      
      console.log('Mapped addresses:');
      console.log('  visitingAddress:', company.visitingAddress);
      console.log('  businessAddress:', company.businessAddress);
      
      console.log('Display logic result:');
      if (address && (address.street || address.postalCode || address.city)) {
        const addressParts = [];
        if (address.street) addressParts.push(address.street);
        if (address.postalCode && address.city) addressParts.push(`${address.postalCode} ${address.city}`);
        const displayAddress = addressParts.join(', ');
        console.log(`  ✅ Address: "${displayAddress}"`);
      } else {
        console.log('  ❌ No address to display');
      }
      
    } catch (error) {
      console.error(`💥 Error testing ${testCompany.name}:`, error.message);
    }
  }
}

testAddressFix();
