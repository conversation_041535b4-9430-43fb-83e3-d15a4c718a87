# Managing Director Validation Fix

## 🎯 **Overview**
This document describes the fix for a Convex validation error that occurred when creating projects with Brønnøysundregisteret company data containing detailed managing director information.

## ✅ **Issue Resolved**

### **Problem Description** ❌
When creating a project with a business customer that had detailed managing director information from Brønnøysundregisteret, the following error occurred:

```
ArgumentValidationError: Value does not match validator.
Path: .brregData.managingDirector
Value: {birthDate: "1987-05-04", firstName: "<PERSON>to<PERSON>", fullName: "<PERSON><PERSON><PERSON> Stenstad", lastName: "Stenstad"}
Validator: v.string()
```

### **Root Cause Identified** 🔍
The Brønnøysundregisteret API returns comprehensive company information including detailed managing director data and additional company fields that were missing from the Convex validators.

**Data Structure Mismatch:**
- **Expected**: Limited fields with `managingDirector: string`
- **Received**: Complete company data including:
  - `managingDirector: object` with detailed person information
  - `industryCode: string` (NACE industry code)
  - `industryDescription: string` (industry description)
  - `organizationNumber: string` (alternative field name)
  - `status: string` (company status)

### **Solution Implemented** ✅
- **Updated Schema**: Modified Convex validators to accept both string and object formats
- **Enhanced Display Logic**: Added helper functions to handle both data formats
- **Fixed Update Functions**: Updated Brønnøysundregisteret update functions to use roles API
- **Backward Compatibility**: Maintained support for existing string-format data

## 🔧 **Technical Implementation**

### **Files Modified (4 total)**

#### **convex/schema.ts**
**Before:**
```typescript
brregData: v.optional(v.object({
  name: v.optional(v.string()),
  orgNumber: v.optional(v.string()),
  managingDirector: v.optional(v.string()),
  // Limited fields only
}))
```

**After:**
```typescript
brregData: v.optional(v.object({
  name: v.optional(v.string()),
  orgNumber: v.optional(v.string()),
  organizationNumber: v.optional(v.string()), // Alternative field name
  status: v.optional(v.string()),    // Company status (active, inactive, etc.)
  industryCode: v.optional(v.string()), // Industry code (NACE code)
  industryDescription: v.optional(v.string()), // Industry description
  managingDirector: v.optional(v.union(
    v.string(), // Legacy format: just the name as string
    v.object({  // New detailed format from Brønnøysundregisteret
      birthDate: v.optional(v.string()),
      firstName: v.optional(v.string()),
      fullName: v.optional(v.string()),
      lastName: v.optional(v.string())
    })
  )),
  // Address fields...
}))
```

#### **convex/customers.ts**
Updated both `create` and `update` mutations with the same union validator to match the schema.

#### **Step2CustomerInfo.tsx & CreateProject.tsx**
**Added Helper Function:**
```typescript
// Helper function to get managing director display name
const getManagingDirectorName = (managingDirector: any): string => {
  if (typeof managingDirector === 'string') {
    return managingDirector;
  }
  if (typeof managingDirector === 'object' && managingDirector) {
    return managingDirector.fullName || `${managingDirector.firstName || ''} ${managingDirector.lastName || ''}`.trim();
  }
  return '';
};
```

**Updated Display Logic:**
```typescript
// Before
<span className="ml-2 text-jobblogg-text-medium">{selectedCustomer.brregData.managingDirector}</span>

// After
<span className="ml-2 text-jobblogg-text-medium">{getManagingDirectorName(selectedCustomer.brregData.managingDirector)}</span>
```

**Enhanced Update Functions:**
```typescript
// Fetch managing director information from roles API
let managingDirector = null;
try {
  const rolesResponse = await fetch(`https://data.brreg.no/enhetsregisteret/api/enheter/${customer.orgNumber}/roller`);
  
  if (rolesResponse.ok) {
    const rolesData = await rolesResponse.json();
    const managingDirectorGroup = rolesData.rollegrupper?.find((group: any) =>
      group.type?.kode === 'DAGL'
    );

    if (managingDirectorGroup?.roller?.[0]?.person) {
      const person = managingDirectorGroup.roller[0].person;
      const navn = person.navn;

      managingDirector = {
        firstName: navn.fornavn || '',
        lastName: navn.etternavn || '',
        fullName: [navn.fornavn, navn.mellomnavn, navn.etternavn].filter(Boolean).join(' '),
        birthDate: person.fodselsdato
      };
    }
  }
} catch (error) {
  console.warn('Failed to fetch managing director:', error);
}
```

## 🎯 **Data Format Support**

### **Legacy String Format** ✅
```typescript
managingDirector: "Kristoffer Stenstad"
```

### **New Object Format** ✅
```typescript
managingDirector: {
  firstName: "Kristoffer",
  lastName: "Stenstad", 
  fullName: "Kristoffer Stenstad",
  birthDate: "1987-05-04"
}
```

### **Display Logic**
The helper function `getManagingDirectorName()` handles both formats:
- **String**: Returns the string directly
- **Object**: Returns `fullName` if available, otherwise constructs name from `firstName` and `lastName`
- **Null/Undefined**: Returns empty string

## 🚀 **Production Benefits**

### **Error Resolution**
- **No More Validation Errors**: Project creation with detailed managing director data now works correctly
- **Complete Data Storage**: All managing director information from Brønnøysundregisteret is properly stored
- **Data Integrity**: Both legacy and new data formats are supported

### **Enhanced Data Quality**
- **Detailed Information**: Birth dates and structured names are preserved
- **Flexible Display**: Can show full names or construct from parts as needed
- **Future-Proof**: Schema supports additional fields that may be added later

### **Improved User Experience**
- **Seamless Operation**: Users can create projects without encountering validation errors
- **Rich Information**: More detailed managing director information is available when needed
- **Consistent Display**: Managing director names are displayed consistently regardless of data format

### **Development Benefits**
- **Backward Compatibility**: Existing customers with string-format managing director data continue to work
- **Type Safety**: Union types provide proper TypeScript validation
- **Error Prevention**: Robust handling prevents future validation issues
- **Maintainable Code**: Clear helper functions make the code easier to understand and maintain

## ✅ **Implementation Complete**

### **Quality Assurance** ✅
- ✅ **No compilation errors** - All TypeScript types are consistent
- ✅ **Schema validation** - Union validators handle both data formats
- ✅ **Backward compatibility** - Existing string data continues to work
- ✅ **Complete coverage** - All relevant components updated

### **Validation** ✅
- ✅ **Project creation works** - No more validation errors with detailed managing director data
- ✅ **Data display correct** - Managing director names display properly in both formats
- ✅ **Update functionality** - Brønnøysundregisteret updates fetch complete managing director information
- ✅ **Error handling** - Graceful fallbacks for missing or malformed data

The managing director validation fix is now complete and resolves all validation errors when creating projects with detailed Brønnøysundregisteret company data. The schema now properly supports both legacy string format and new detailed object format for managing director information.
