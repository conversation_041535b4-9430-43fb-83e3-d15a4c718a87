# JobbLogg Database Reset Guide

## Overview

The JobbLogg database reset system provides comprehensive tools for safely removing all user-generated data while preserving the database schema structure. This is essential for development, testing, and demo environments.

## ⚠️ Safety Warning

**This system is designed for development and testing environments only!**

- All operations are **irreversible**
- Data cannot be recovered once deleted
- Always backup production data before any operations
- Never run in production environments

## System Architecture

### Database Tables (Deletion Order)

The system respects foreign key relationships by deleting data in the correct order:

1. **`typingIndicators`** - Real-time typing status (no dependencies)
2. **`messages`** - Chat messages and reactions (depends on logEntries)
3. **`imageLikes`** - Customer image likes (depends on logEntries and projects)
4. **`logEntries`** - Project log entries and images (depends on projects)
5. **`projects`** - All projects including archived and job data (depends on customers)
6. **`customers`** - Both regular customers and contractor companies (no dependencies after projects)
7. **`users`** - Contractor onboarding data (no dependencies after customers)
8. **File Storage** - Images and attachments from Convex storage (optional)

### What Gets Deleted

#### Core Data
- ✅ All projects (active and archived)
- ✅ All project log entries
- ✅ All chat messages and reactions
- ✅ All typing indicators
- ✅ All image likes and customer sessions
- ✅ All customer records (regular and contractor companies)
- ✅ All user records (contractor onboarding data)

#### Optional Data
- 🔧 File storage (images, attachments) - configurable
- 🔧 Job photos from project data - configurable

#### What's Preserved
- ✅ Database schema definitions
- ✅ All indexes and constraints
- ✅ System configuration
- ✅ Clerk authentication system (external)

## Usage Methods

### 1. Interactive Shell Script (Recommended)

```bash
# Run the interactive script
./scripts/reset-database.sh
```

**Features:**
- User-friendly menu interface
- Multiple operation modes
- Built-in safety confirmations
- Comprehensive data analysis
- Progress reporting

**Menu Options:**
1. **Check comprehensive database state** - View current data counts
2. **Dry-run analysis** - Preview what would be deleted
3. **Comprehensive database reset** - Full reset with options
4. **Legacy project data clear** - Backward compatibility
5. **Create test data** - Generate sample data
6. **Full reset + create test data** - Complete refresh
7. **Exit**

### 2. Direct Convex Function Calls

#### Dry-Run Analysis (Safe)
```bash
# Preview what would be deleted (no actual deletion)
npx convex run clearAllProjectData:dryRunReset '{"includeFileStorage": false}'

# Include file storage analysis
npx convex run clearAllProjectData:dryRunReset '{"includeFileStorage": true}'
```

#### Get Data Count
```bash
# Check current database state
npx convex run clearAllProjectData:getProjectDataCount '{}'
```

#### Comprehensive Reset
```bash
# Database records only
npx convex run clearAllProjectData:comprehensiveReset '{
  "confirmationCode": "DELETE_ALL_PROJECT_DATA",
  "dryRun": false,
  "includeFileStorage": false
}'

# Database records + file storage
npx convex run clearAllProjectData:comprehensiveReset '{
  "confirmationCode": "DELETE_ALL_PROJECT_DATA",
  "dryRun": false,
  "includeFileStorage": true
}'
```

#### Legacy Function (Backward Compatibility)
```bash
# Original function (may not include contractor data)
npx convex run clearAllProjectData:clearAllProjectData '{
  "confirmationCode": "DELETE_ALL_PROJECT_DATA"
}'
```

## Safety Features

### 1. Confirmation Codes
All destructive operations require the exact confirmation code:
```
"DELETE_ALL_PROJECT_DATA"
```

### 2. Dry-Run Mode
Preview operations without executing:
```javascript
{
  "confirmationCode": "DELETE_ALL_PROJECT_DATA",
  "dryRun": true,
  "includeFileStorage": false
}
```

### 3. Environment Protection
```javascript
{
  "confirmationCode": "DELETE_ALL_PROJECT_DATA",
  "environment": "production" // Will throw error
}
```

### 4. Comprehensive Logging
- Real-time progress updates
- Detailed deletion counts
- Error reporting with partial results
- Operation summaries

### 5. Error Handling
- Graceful error recovery
- Partial deletion reporting
- Database consistency warnings
- Retry recommendations

## Output Examples

### Data Count Analysis
```
📊 COMPREHENSIVE DATABASE STATE:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📱 CHAT SYSTEM:
   • Typing Indicators: 3
   • Chat Messages: 45
   • Image Likes: 12

📋 PROJECT SYSTEM:
   • Log Entries: 28
   • Projects: 8 (2 archived, 6 active)

👥 USER MANAGEMENT:
   • User Records: 5 (4 completed onboarding, 1 pending)
   • Customers: 15 (3 contractor companies, 12 regular customers)

🔢 TOTAL RECORDS: 116
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

### Dry-Run Analysis
```
🔍 DRY-RUN MODE: No data will actually be deleted
📊 Found 3 typing indicators
📊 Found 45 chat messages
📊 Found 12 image likes
📊 Found 28 log entries
📊 Found 15 associated images
📊 Found 8 projects
📊 Found 15 customers (3 contractor companies, 12 regular customers)
📊 Found 5 user records
📊 TOTAL ANALYZED: 131 records
🔍 DRY-RUN COMPLETE: No data was actually deleted
💡 To execute the deletion, run again with dryRun: false
```

### Successful Reset
```
🎉 COMPREHENSIVE DATABASE DELETION COMPLETED SUCCESSFULLY!
📊 DELETION SUMMARY:
   • Typing Indicators: 3
   • Chat Messages: 45
   • Image Likes: 12
   • Log Entries: 28
   • Projects: 8
   • Customers: 15
   • User Records: 5
   • File Storage Items: 15
   • TOTAL DELETED: 131 records
✅ Database is now completely clean for fresh testing!
🔄 All schema structures and indexes remain intact
```

## Best Practices

### 1. Always Start with Analysis
```bash
# Check current state
npx convex run clearAllProjectData:getProjectDataCount '{}'

# Run dry-run to preview
npx convex run clearAllProjectData:dryRunReset '{}'
```

### 2. Use Interactive Script for Safety
The shell script provides the safest interface with built-in confirmations and progress reporting.

### 3. Document Operations
Keep records of when and why resets were performed, especially in shared development environments.

### 4. Verify Results
Always check the final state after operations:
```bash
npx convex run clearAllProjectData:getProjectDataCount '{}'
```

### 5. Test Data Creation
After reset, create consistent test data:
```bash
npx convex run testDataUtilities:createTestData '{
  "confirmationCode": "CREATE_TEST_DATA",
  "testUserId": "your-clerk-user-id"
}'
```

## Troubleshooting

### Common Issues

#### 1. Partial Deletion Errors
If deletion fails partway through:
- Check the partial results in the error message
- Run the operation again to complete cleanup
- Database may be in inconsistent state until completed

#### 2. File Storage Errors
If file deletion fails:
- Files may remain in storage but database references are removed
- This is generally safe but may consume storage space
- Manual cleanup may be required

#### 3. Permission Errors
Ensure you have proper Convex permissions:
- Admin access to the Convex project
- Ability to run mutations
- Proper environment configuration

### Recovery Procedures

#### 1. Incomplete Reset
```bash
# Run the operation again to complete
npx convex run clearAllProjectData:comprehensiveReset '{
  "confirmationCode": "DELETE_ALL_PROJECT_DATA",
  "dryRun": false,
  "includeFileStorage": false
}'
```

#### 2. Verify Clean State
```bash
# Check all tables are empty
npx convex run clearAllProjectData:getProjectDataCount '{}'
```

#### 3. Recreate Test Environment
```bash
# Use the interactive script for full reset + test data
./scripts/reset-database.sh
# Choose option 6: Full reset + create test data
```

## Development Workflow

### Typical Development Cycle
1. **Start Development Session**
   ```bash
   ./scripts/reset-database.sh
   # Choose option 1: Check database state
   ```

2. **Clean Slate for Testing**
   ```bash
   ./scripts/reset-database.sh
   # Choose option 3: Comprehensive reset
   ```

3. **Create Test Scenario**
   ```bash
   ./scripts/reset-database.sh
   # Choose option 5: Create test data
   ```

4. **Full Environment Refresh**
   ```bash
   ./scripts/reset-database.sh
   # Choose option 6: Full reset + test data
   ```

This system provides a robust, safe, and comprehensive solution for database management in JobbLogg development environments.
