# Company Lookup Address Issue Investigation & Fix

## 🔍 **Issue Description**
When searching for companies using the Brønnøysundregisteret API integration, many companies were showing "Ingen adresse registrert" (No address registered) in the search results dropdown, even though these companies should have address information.

## 🕵️ **Investigation Process**

### 1. API Response Analysis
Created debug utilities to inspect raw API responses from Brønnøysundregisteret:
- `scripts/test-api-response.js` - Node.js script to test API responses
- `src/utils/debugCompanyAPI.ts` - Browser debug functions
- Updated test page with debug buttons

### 2. Key Findings
Testing with major Norwegian companies revealed the root cause:

**Equinor ASA (*********):**
```json
{
  "beliggenhetsadresse": undefined,
  "forretningsadresse": {
    "adresse": ["Forusbeen 50"],
    "postnummer": "4035",
    "poststed": "STAVANGER"
  }
}
```

**DNB Bank ASA (*********):**
```json
{
  "beliggenhetsadresse": undefined,
  "forretningsadresse": {
    "adresse": ["Dronning Eufemias gate 30"],
    "postnummer": "0191",
    "poststed": "OSLO"
  }
}
```

**Telenor ASA (*********):**
```json
{
  "beliggenhetsadresse": undefined,
  "forretningsadresse": {
    "adresse": ["Snarøyveien 30"],
    "postnummer": "1360",
    "poststed": "FORNEBU"
  }
}
```

### 3. Root Cause Identified
**Many Norwegian companies only have `forretningsadresse` (business address) registered, but no `beliggenhetsadresse` (visiting address).** Our original code only displayed addresses when `visitingAddress` existed, ignoring the `businessAddress` field.

## 🔧 **Solution Implemented**

### 1. Updated CompanyLookup Component Display Logic
**File**: `src/components/CompanyLookup/CompanyLookup.tsx`

**Before** (only showed visiting address):
```tsx
{company.visitingAddress && (
  <div className="text-sm text-jobblogg-text-muted">
    {company.visitingAddress.street && (
      <>
        {company.visitingAddress.street}
        {company.visitingAddress.postalCode && company.visitingAddress.city && (
          <>, {company.visitingAddress.postalCode} {company.visitingAddress.city}</>
        )}
      </>
    )}
  </div>
)}
```

**After** (shows visiting address OR business address):
```tsx
{(() => {
  // Display visiting address if available, otherwise business address
  const address = company.visitingAddress || company.businessAddress;
  if (address && (address.street || address.postalCode || address.city)) {
    return (
      <div className="text-sm text-jobblogg-text-muted">
        {address.street && (
          <>
            {address.street}
            {address.postalCode && address.city && (
              <>, {address.postalCode} {address.city}</>
            )}
          </>
        )}
        {!address.street && address.postalCode && address.city && (
          <>{address.postalCode} {address.city}</>
        )}
      </div>
    );
  }
  return null;
})()}
```

### 2. Updated Auto-fill Logic in Step2CustomerInfo
**File**: `src/pages/CreateProject/steps/Step2CustomerInfo.tsx`

**Before**:
```tsx
streetAddress: company.visitingAddress?.street || '',
postalCode: company.visitingAddress?.postalCode || '',
city: company.visitingAddress?.city || ''
```

**After**:
```tsx
const address = company.visitingAddress || company.businessAddress;
streetAddress: address?.street || '',
postalCode: address?.postalCode || '',
city: address?.city || ''
```

### 3. CreateProject Form Already Had Fallback
The main CreateProject form already had proper fallback logic implemented.

## ✅ **Verification Results**

Testing the fix with known companies:

| Company | Org Number | Before Fix | After Fix |
|---------|------------|------------|-----------|
| Equinor ASA | ********* | "Ingen adresse registrert" | "Forusbeen 50, 4035 STAVANGER" |
| DNB Bank ASA | ********* | "Ingen adresse registrert" | "Dronning Eufemias gate 30, 0191 OSLO" |
| Telenor ASA | ********* | "Ingen adresse registrert" | "Snarøyveien 30, 1360 FORNEBU" |

## 📊 **Impact Assessment**

### Positive Impact:
- ✅ Major Norwegian companies now display proper addresses
- ✅ Auto-fill functionality works for business addresses
- ✅ Improved user experience and data accuracy
- ✅ No breaking changes to existing functionality

### Address Type Priority:
1. **Visiting Address** (`beliggenhetsadresse`) - preferred when available
2. **Business Address** (`forretningsadresse`) - fallback for most companies
3. **No Address** - only when neither is available

## 🧪 **Testing Recommendations**

### Manual Testing:
1. Search for "Equinor" - should show address
2. Search for "DNB" - should show address  
3. Search for "Telenor" - should show address
4. Select companies and verify auto-fill works

### Debug Testing:
- Use `/company-lookup-test.html` debug buttons
- Check browser console for detailed API responses
- Use `debugCompanyByOrgNumber('*********')` in console

## 📝 **Documentation Updates**

- Updated `docs/COMPANY_LOOKUP.md` with correct organization numbers
- Added troubleshooting section about address display
- Updated test company table with address type information

## 🔮 **Future Considerations**

1. **Address Type Indicators**: Could show "(Forretningsadresse)" vs "(Besøksadresse)" labels
2. **Multiple Addresses**: Some companies might have both - could offer choice
3. **Address Validation**: Could validate Norwegian postal codes and addresses
4. **Caching**: Cache frequently searched companies to reduce API calls

## 🎯 **Conclusion**

The address display issue has been successfully resolved. The system now properly displays business addresses as fallback when visiting addresses are not available, significantly improving the user experience for Norwegian company lookups.
