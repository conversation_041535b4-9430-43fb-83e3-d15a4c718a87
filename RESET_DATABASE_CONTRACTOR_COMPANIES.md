# Database Reset Script - Contractor Company Management

## Overview

The `reset-database.sh` script has been enhanced with comprehensive contractor company profile (bedriftsprofiler) management capabilities. This update provides detailed analysis, validation, and safe deletion of business data during database reset operations.

## New Features

### 🏢 Contractor Company Analysis
- **Option 3**: Analyze contractor companies (bedriftsprofiler)
- **Option 4**: Get detailed contractor company information  
- **Option 5**: Validate contractor company data integrity

### 🔒 Enhanced Safety Measures
- Specific confirmation prompt for contractor company deletion
- Detailed business data impact analysis before reset
- Comprehensive logging of what contractor companies will be deleted
- Data relationship validation and orphaned record detection

### 📊 Comprehensive Reporting
- Breakdown of contractor vs regular customers
- Project relationships with contractor companies
- Brønnøysundregisteret data tracking
- User onboarding status correlation

## New Menu Options

### Analysis & Inspection (Enhanced)

#### Option 3: Analyze Contractor Companies
```bash
🏢 Analyzing contractor company profiles (bedriftsprofiler)...
```
- Shows count of contractor companies vs regular customers
- Lists each contractor company with basic details
- Identifies data integrity issues (orphaned records)
- Reports project relationships
- Provides deletion impact assessment

#### Option 4: Detailed Contractor Company Information
```bash
🔍 Getting detailed contractor company information...
```
- Complete business information for each contractor company
- Contact details, addresses, organization numbers
- Brønnøysundregisteret integration data
- User relationship mapping
- Timestamps and audit information

#### Option 5: Data Integrity Validation
```bash
🔍 Validating contractor company data integrity...
```
- Checks for orphaned contractor companies (missing user records)
- Identifies users with invalid contractor company references
- Detects duplicate organization numbers
- Validates required field completeness
- Reports relationship consistency issues

### Enhanced Reset Process (Option 6)

The comprehensive database reset now includes:

1. **Pre-Reset Analysis**: Automatic contractor company impact analysis
2. **Business Data Warning**: Clear indication of what business data will be lost
3. **Double Confirmation**: Requires typing `DELETE_CONTRACTOR_COMPANIES` to confirm
4. **Relationship Handling**: Proper deletion order respecting foreign key constraints

## Technical Implementation

### New Convex Functions

#### `convex/resetDatabase.ts`
- `analyzeContractorCompanies`: Comprehensive contractor company analysis
- `getContractorCompanyDetails`: Detailed business information extraction
- `validateContractorCompanyIntegrity`: Data consistency validation

### Data Relationships Handled

1. **Users → Contractor Companies**: `users.contractorCompanyId` → `customers._id`
2. **Contractor Companies → Users**: `customers.contractorUserId` → `users.clerkUserId`
3. **Projects → Contractor Companies**: `projects.customerId` → `customers._id`
4. **Messages → Projects**: Chat messages linked to contractor projects

### Deletion Order (Respects Foreign Keys)

1. Typing indicators (no dependencies)
2. Chat messages and reactions (depends on projects)
3. Image likes and customer sessions (depends on projects)
4. Log entries and associated images (depends on projects)
5. Projects (depends on customers)
6. **Customers (including contractor companies)** ← Enhanced handling
7. User records (contractor onboarding data)
8. File storage items (if requested)

## Safety Features

### Contractor-Specific Confirmations

```bash
⚠️  CRITICAL: All contractor company profiles (bedriftsprofiler) will be permanently deleted!
⚠️  This includes business information, contact details, and Brønnøysundregisteret data!
⚠️  Users will need to complete contractor onboarding again after reset!

Type 'DELETE_CONTRACTOR_COMPANIES' to confirm deletion of business data:
```

### Pre-Reset Impact Analysis

Before any deletion, the script automatically shows:
- Number of contractor companies that will be deleted
- Projects linked to contractor companies
- User onboarding data that will be lost
- Brønnøysundregisteret data that will be removed

### Data Integrity Checks

- **Orphaned Companies**: Contractor companies without user records
- **Orphaned Users**: Users referencing non-existent contractor companies
- **Duplicate Org Numbers**: Multiple companies with same organization number
- **Missing Required Data**: Companies lacking essential business information

## Usage Examples

### Analyze Before Reset
```bash
./reset-database.sh
# Choose option 3: Analyze contractor companies
# Review the business data that would be affected
# Choose option 5: Validate data integrity
# Check for any issues that need attention
```

### Safe Reset with Business Data Awareness
```bash
./reset-database.sh
# Choose option 6: Comprehensive database reset
# Review automatic contractor company analysis
# Confirm understanding of business data deletion
# Type 'DELETE_CONTRACTOR_COMPANIES' when prompted
```

### Detailed Business Data Review
```bash
./reset-database.sh
# Choose option 4: Get detailed contractor company information
# Review complete business profiles before deletion
```

## Output Examples

### Contractor Company Analysis Output
```
🏢 CONTRACTOR COMPANY ANALYSIS
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 CUSTOMER BREAKDOWN:
   • Total customers: 15
   • Contractor companies: 3
   • Regular customers: 12

🏢 CONTRACTOR COMPANY DETAILS:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📋 Company: Byggmester Hansen AS
   • Org Number: *********
   • Contact Person: Ole Hansen
   • Phone: +47 12345678
   • Email: <EMAIL>
   • Address: Storgata 15, 0123 Oslo
   • Contractor User ID: user_abc123
   • Onboarding Status: Completed
   • Created: 15.07.2025, 14:30:00
   • Last Updated: 16.07.2025, 09:15:00
   • Brreg Data: Available (last fetched: 16.07.2025, 09:00:00)
```

### Data Integrity Validation Output
```
🔍 CONTRACTOR COMPANY DATA INTEGRITY VALIDATION
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 VALIDATION RESULTS:
   • Total contractor companies: 3
   • Orphaned companies: 0
   • Orphaned users: 0
   • Companies with missing data: 1
   • Projects linked to contractors: 8
   • Duplicate org numbers: 0

⚠️  WARNINGS:
   • Found 1 companies with missing required fields

✅ No critical data integrity issues found
```

## Best Practices

### Before Database Reset
1. **Always run analysis first** (Options 3-5) to understand impact
2. **Review detailed company information** if business data is critical
3. **Validate data integrity** to identify any issues
4. **Document important business information** if needed for recovery

### During Reset
1. **Read all warnings carefully** before confirming
2. **Understand that business data deletion is permanent**
3. **Ensure you have the correct confirmation codes**
4. **Monitor the output for any errors**

### After Reset
1. **Verify the final state** matches expectations
2. **Test contractor onboarding flow** if needed
3. **Recreate test data** with representative business profiles

## Error Handling

The script includes comprehensive error handling for:
- **Network issues** during Convex function calls
- **Authentication problems** with Convex
- **Data consistency errors** during analysis
- **Partial deletion failures** with rollback information

## Development vs Production

⚠️ **CRITICAL**: This script is designed for development/testing environments only!

- **Production Safety**: Script checks for production environment and blocks execution
- **Confirmation Codes**: Multiple confirmation steps prevent accidental execution
- **Business Data Protection**: Special handling for contractor company profiles
- **Audit Trail**: Comprehensive logging of all operations

## Migration Considerations

When updating from older versions of the reset script:
1. **New Convex Functions**: Ensure `convex/resetDatabase.ts` is deployed
2. **Enhanced Confirmations**: Be prepared for additional confirmation prompts
3. **Extended Analysis**: Analysis operations now take longer due to detailed reporting
4. **Menu Changes**: Menu options have been renumbered (Exit is now option 10)

---

**Updated**: 16. juli 2025  
**Version**: v2.0.0 with Contractor Company Management  
**Compatibility**: JobbLogg with contractor onboarding system
