#!/bin/bash

# JobbLogg Comprehensive Database Reset Script
# This script safely removes all user-generated data while preserving database schema structure

set -e  # Exit on any error

echo "🚨 JobbLogg Comprehensive Database Reset Utility"
echo "================================================"
echo ""
echo "⚠️  WARNING: This will permanently delete ALL user-generated data!"
echo "⚠️  Including: projects, customers, users, chat messages, contractor data"
echo "⚠️  BUSINESS DATA: Contractor company profiles (bedriftsprofiler) will be deleted!"
echo "⚠️  Only use in development/testing environments!"
echo ""
echo "✅ PRESERVES: Database schema, indexes, system configuration"
echo ""

# Check if we're in the right directory
if [ ! -f "convex/schema.ts" ]; then
    echo "❌ Error: Please run this script from the JobbLogg project root directory"
    exit 1
fi

# Check if Convex CLI is available
if ! command -v npx &> /dev/null; then
    echo "❌ Error: npx is not available. Please install Node.js and npm."
    exit 1
fi

# Function to get comprehensive data count
get_data_count() {
    echo "📊 Getting comprehensive database state..."
    npx convex run clearAllProjectData:getProjectDataCount '{}'
    echo ""
}

# Function to analyze contractor company profiles specifically
analyze_contractor_companies() {
    echo "🏢 Analyzing contractor company profiles (bedriftsprofiler)..."
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    npx convex run resetDatabase:analyzeContractorCompanies '{}'
    echo ""
}

# Function to get detailed contractor company information
get_contractor_company_details() {
    echo "🔍 Getting detailed contractor company information..."
    echo "This will show all contractor companies with their business details."
    echo ""
    read -p "Continue with detailed analysis? (y/n): " continue_analysis

    if [ "$continue_analysis" = "y" ] || [ "$continue_analysis" = "Y" ]; then
        npx convex run resetDatabase:getContractorCompanyDetails '{}'
    else
        echo "❌ Detailed analysis cancelled"
    fi
    echo ""
}

# Function to validate contractor company data integrity
validate_contractor_integrity() {
    echo "🔍 Validating contractor company data integrity..."
    echo "This will check for orphaned records, missing data, and relationship issues."
    echo ""
    npx convex run resetDatabase:validateContractorCompanyIntegrity '{}'
    echo ""
}

# Function to run dry-run analysis
dry_run_analysis() {
    echo "🔍 Running dry-run analysis (no data will be deleted)..."
    echo ""
    echo "Analysis options:"
    echo "1) Standard analysis (database records only)"
    echo "2) Include file storage analysis"
    echo "3) Focus on contractor companies only"
    echo ""
    read -p "Choose analysis type (1-3): " analysis_type

    case $analysis_type in
        1)
            npx convex run clearAllProjectData:dryRunReset '{"includeFileStorage": false}'
            ;;
        2)
            npx convex run clearAllProjectData:dryRunReset '{"includeFileStorage": true}'
            ;;
        3)
            analyze_contractor_companies
            ;;
        *)
            echo "❌ Invalid choice. Running standard analysis..."
            npx convex run clearAllProjectData:dryRunReset '{"includeFileStorage": false}'
            ;;
    esac
    echo ""
}

# Function to create test data
create_test_data() {
    echo "🧪 Creating test data..."
    echo "Please enter your Clerk user ID (you can find this in the Convex dashboard):"
    read -r USER_ID

    if [ -z "$USER_ID" ]; then
        echo "❌ Error: User ID is required"
        exit 1
    fi

    npx convex run testDataUtilities:createTestData "{\"confirmationCode\": \"CREATE_TEST_DATA\", \"testUserId\": \"$USER_ID\"}"
    echo ""
}

# Function to clear all data (legacy)
clear_all_data() {
    echo "🗑️  Clearing all data (legacy function)..."
    npx convex run clearAllProjectData:clearAllProjectData '{"confirmationCode": "DELETE_ALL_PROJECT_DATA"}'
    echo ""
}

# Function to comprehensive reset with contractor company confirmation
comprehensive_reset() {
    echo ""
    echo "🔧 COMPREHENSIVE DATABASE RESET OPTIONS:"
    echo "1) Database records only (preserve file storage)"
    echo "2) Database records + file storage (complete wipe)"
    echo ""
    read -p "Choose option (1-2): " storage_choice

    local include_storage="false"
    if [ "$storage_choice" = "2" ]; then
        include_storage="true"
        echo ""
        echo "⚠️  WARNING: This will also delete all images and files from Convex storage!"
        echo "⚠️  This action cannot be undone!"
        echo ""
    fi

    # Show contractor company impact before proceeding
    echo "🏢 CONTRACTOR COMPANY IMPACT ANALYSIS:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    analyze_contractor_companies

    echo "⚠️  CRITICAL: All contractor company profiles (bedriftsprofiler) will be permanently deleted!"
    echo "⚠️  This includes business information, contact details, and Brønnøysundregisteret data!"
    echo "⚠️  Users will need to complete contractor onboarding again after reset!"
    echo ""
    read -p "Type 'DELETE_CONTRACTOR_COMPANIES' to confirm deletion of business data: " contractor_confirm

    if [ "$contractor_confirm" != "DELETE_CONTRACTOR_COMPANIES" ]; then
        echo "❌ Contractor company deletion not confirmed. Operation cancelled."
        return 1
    fi

    echo "🗑️  Executing comprehensive database reset..."
    npx convex run clearAllProjectData:comprehensiveReset "{\"confirmationCode\": \"DELETE_ALL_PROJECT_DATA\", \"dryRun\": false, \"includeFileStorage\": $include_storage}"
    echo ""
}

# Main menu
while true; do
    echo "What would you like to do?"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "📊 ANALYSIS & INSPECTION:"
    echo "1) Check comprehensive database state"
    echo "2) Dry-run analysis (preview what would be deleted)"
    echo "3) Analyze contractor companies (bedriftsprofiler)"
    echo "4) Get detailed contractor company information"
    echo "5) Validate contractor company data integrity"
    echo ""
    echo "🗑️  DATA MANAGEMENT:"
    echo "6) Comprehensive database reset (recommended)"
    echo "7) Legacy project data clear (backward compatibility)"
    echo ""
    echo "🧪 TESTING:"
    echo "8) Create test data"
    echo "9) Full reset + create test data"
    echo ""
    echo "10) Exit"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    read -p "Enter your choice (1-10): " choice

    case $choice in
        1)
            get_data_count
            ;;
        2)
            dry_run_analysis
            ;;
        3)
            analyze_contractor_companies
            ;;
        4)
            get_contractor_company_details
            ;;
        5)
            validate_contractor_integrity
            ;;
        6)
            echo ""
            echo "⚠️  COMPREHENSIVE DATABASE RESET"
            echo "⚠️  This will delete ALL user-generated data:"
            echo "   • All projects (including archived)"
            echo "   • All customers (including contractor companies/bedriftsprofiler)"
            echo "   • All user records (contractor onboarding data)"
            echo "   • All chat messages and reactions"
            echo "   • All log entries and image likes"
            echo "   • All typing indicators"
            echo "   • Optionally: All file storage (images/attachments)"
            echo ""
            echo "🏢 BUSINESS DATA IMPACT:"
            echo "   • Contractor company profiles will be permanently deleted"
            echo "   • Business information, contact details, org numbers"
            echo "   • Brønnøysundregisteret integration data"
            echo "   • Users will need to re-complete contractor onboarding"
            echo ""
            echo "✅ PRESERVES: Database schema, indexes, system configuration"
            echo ""
            echo "This action cannot be undone!"
            read -p "Type 'RESET' to confirm: " confirm

            if [ "$confirm" = "RESET" ]; then
                echo "📊 Current state before reset:"
                get_data_count

                if comprehensive_reset; then
                    echo "✅ Comprehensive database reset completed!"

                    echo "📊 Final state after reset:"
                    get_data_count

                    echo "🎉 Database is now completely clean for fresh testing!"
                else
                    echo "❌ Reset operation was cancelled or failed"
                fi
            else
                echo "❌ Operation cancelled"
            fi
            echo ""
            ;;
        7)
            echo ""
            echo "⚠️  LEGACY PROJECT DATA CLEAR"
            echo "⚠️  This uses the legacy function (may not include all contractor data)"
            echo "⚠️  For complete contractor company deletion, use option 5 instead"
            echo "This action cannot be undone!"
            read -p "Type 'yes' to confirm: " confirm

            if [ "$confirm" = "yes" ]; then
                clear_all_data
                echo "✅ Legacy project data cleared successfully!"
            else
                echo "❌ Operation cancelled"
            fi
            echo ""
            ;;
        8)
            create_test_data
            echo "✅ Test data created successfully!"
            echo ""
            ;;
        9)
            echo ""
            echo "⚠️  FULL RESET + TEST DATA CREATION"
            echo "⚠️  This will:"
            echo "   1. Delete all existing user data (including contractor companies)"
            echo "   2. Create fresh test data"
            echo ""
            echo "🏢 CONTRACTOR COMPANY IMPACT:"
            echo "   • All contractor company profiles will be deleted"
            echo "   • Test data may include sample contractor companies"
            echo ""
            echo "This action cannot be undone!"
            read -p "Type 'RESET' to confirm: " confirm

            if [ "$confirm" = "RESET" ]; then
                echo "📊 Current state before reset:"
                get_data_count

                if comprehensive_reset; then
                    echo "✅ All data cleared!"

                    create_test_data
                    echo "✅ Test data created!"

                    echo "📊 Final state after reset:"
                    get_data_count

                    echo "🎉 Full database reset with test data completed successfully!"
                else
                    echo "❌ Reset operation was cancelled or failed"
                fi
            else
                echo "❌ Operation cancelled"
            fi
            echo ""
            ;;
        10)
            echo "👋 Goodbye!"
            exit 0
            ;;
        *)
            echo "❌ Invalid choice. Please enter 1-10."
            echo ""
            ;;
    esac
done
