import { query, mutation } from './_generated/server';
import { v } from 'convex/values';

/**
 * SAFE Contractor Onboarding Functions
 * 
 * These functions handle authentication errors gracefully and return
 * safe default values instead of throwing errors.
 */

// Safe version of getContractorOnboardingStatus that doesn't throw auth errors
export const getContractorOnboardingStatusSafe = query({
  args: {
    clerkUserId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // Validate authentication
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        console.log("Safe auth check: No identity found for user", args.clerkUserId);
        return {
          exists: false,
          contractorCompleted: false,
          contractorCompanyId: null,
          authError: true,
          error: "Autentisering ikke fullført"
        };
      }

      // Ensure the requesting user matches the Clerk user ID
      if (identity.subject !== args.clerkUserId) {
        console.log("Safe auth check: User ID mismatch:", { identity: identity.subject, requested: args.clerkUserId });
        return {
          exists: false,
          contractorCompleted: false,
          contractorCompanyId: null,
          authError: true,
          error: "Bruker-ID stemmer ikke overens"
        };
      }

      // Try to find user record
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
        .first();

      return {
        exists: !!user,
        contractorCompleted: user?.contractorCompleted || false,
        contractorCompanyId: user?.contractorCompanyId || null,
        authError: false,
        error: null
      };

    } catch (error) {
      console.error("Safe auth check error:", error);
      return {
        exists: false,
        contractorCompleted: false,
        contractorCompanyId: null,
        authError: true,
        error: error instanceof Error ? error.message : "Ukjent feil"
      };
    }
  },
});

// Safe version of getContractorCompanyByUserId
export const getContractorCompanyByUserIdSafe = query({
  args: {
    clerkUserId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // Validate authentication
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        console.log("Safe company check: No identity found for user", args.clerkUserId);
        return {
          company: null,
          authError: true,
          error: "Autentisering ikke fullført"
        };
      }

      // Ensure the requesting user matches the Clerk user ID
      if (identity.subject !== args.clerkUserId) {
        console.log("Safe company check: User ID mismatch:", { identity: identity.subject, requested: args.clerkUserId });
        return {
          company: null,
          authError: true,
          error: "Bruker-ID stemmer ikke overens"
        };
      }

      // Find user record
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
        .first();

      if (!user || !user.contractorCompanyId) {
        return {
          company: null,
          authError: false,
          error: null
        };
      }

      // Get contractor company
      const company = await ctx.db.get(user.contractorCompanyId);
      return {
        company,
        authError: false,
        error: null
      };

    } catch (error) {
      console.error("Safe company check error:", error);
      return {
        company: null,
        authError: true,
        error: error instanceof Error ? error.message : "Ukjent feil"
      };
    }
  },
});

// Safe version of getOrCreateUser
export const getOrCreateUserSafe = mutation({
  args: {
    clerkUserId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // Validate authentication
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        console.log("Safe user creation: No identity found for user", args.clerkUserId);
        return {
          user: null,
          created: false,
          authError: true,
          error: "Autentisering ikke fullført"
        };
      }

      // Ensure the requesting user matches the Clerk user ID
      if (identity.subject !== args.clerkUserId) {
        console.log("Safe user creation: User ID mismatch:", { identity: identity.subject, requested: args.clerkUserId });
        return {
          user: null,
          created: false,
          authError: true,
          error: "Bruker-ID stemmer ikke overens"
        };
      }

      // Check if user record already exists
      const existingUser = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
        .first();

      if (existingUser) {
        return {
          user: existingUser,
          created: false,
          authError: false,
          error: null
        };
      }

      // Create new user record
      const userId = await ctx.db.insert("users", {
        clerkUserId: args.clerkUserId,
        contractorCompleted: false,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      const newUser = await ctx.db.get(userId);
      return {
        user: newUser,
        created: true,
        authError: false,
        error: null
      };

    } catch (error) {
      console.error("Safe user creation error:", error);
      return {
        user: null,
        created: false,
        authError: true,
        error: error instanceof Error ? error.message : "Ukjent feil"
      };
    }
  },
});
