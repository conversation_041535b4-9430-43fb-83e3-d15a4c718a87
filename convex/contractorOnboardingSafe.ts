import { query, mutation } from './_generated/server';
import { v } from 'convex/values';

/**
 * SAFE Contractor Onboarding Functions
 * 
 * These functions handle authentication errors gracefully and return
 * safe default values instead of throwing errors.
 */

// Safe version of getContractorOnboardingStatus that doesn't throw auth errors
export const getContractorOnboardingStatusSafe = query({
  args: {
    clerkUserId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // Validate authentication
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        console.log("Safe auth check: No identity found for user", args.clerkUserId);
        return {
          exists: false,
          contractorCompleted: false,
          contractorCompanyId: null,
          authError: true,
          error: "Autentisering ikke fullført"
        };
      }

      // Ensure the requesting user matches the Clerk user ID
      if (identity.subject !== args.clerkUserId) {
        console.log("Safe auth check: User ID mismatch:", { identity: identity.subject, requested: args.clerkUserId });
        return {
          exists: false,
          contractorCompleted: false,
          contractorCompanyId: null,
          authError: true,
          error: "Bruker-ID stemmer ikke overens"
        };
      }

      // Try to find user record
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
        .first();

      return {
        exists: !!user,
        contractorCompleted: user?.contractorCompleted || false,
        contractorCompanyId: user?.contractorCompanyId || null,
        authError: false,
        error: null
      };

    } catch (error) {
      console.error("Safe auth check error:", error);
      return {
        exists: false,
        contractorCompleted: false,
        contractorCompanyId: null,
        authError: true,
        error: error instanceof Error ? error.message : "Ukjent feil"
      };
    }
  },
});

// Safe version of getContractorCompanyByUserId
export const getContractorCompanyByUserIdSafe = query({
  args: {
    clerkUserId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // Validate authentication
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        console.log("Safe company check: No identity found for user", args.clerkUserId);
        return {
          company: null,
          authError: true,
          error: "Autentisering ikke fullført"
        };
      }

      // Ensure the requesting user matches the Clerk user ID
      if (identity.subject !== args.clerkUserId) {
        console.log("Safe company check: User ID mismatch:", { identity: identity.subject, requested: args.clerkUserId });
        return {
          company: null,
          authError: true,
          error: "Bruker-ID stemmer ikke overens"
        };
      }

      // Find user record
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
        .first();

      if (!user || !user.contractorCompanyId) {
        return {
          company: null,
          authError: false,
          error: null
        };
      }

      // Get contractor company
      const company = await ctx.db.get(user.contractorCompanyId);
      return {
        company,
        authError: false,
        error: null
      };

    } catch (error) {
      console.error("Safe company check error:", error);
      return {
        company: null,
        authError: true,
        error: error instanceof Error ? error.message : "Ukjent feil"
      };
    }
  },
});

// Safe version of getOrCreateUser
export const getOrCreateUserSafe = mutation({
  args: {
    clerkUserId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // Validate authentication
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        console.log("Safe user creation: No identity found for user", args.clerkUserId);
        return {
          user: null,
          created: false,
          authError: true,
          error: "Autentisering ikke fullført"
        };
      }

      // Ensure the requesting user matches the Clerk user ID
      if (identity.subject !== args.clerkUserId) {
        console.log("Safe user creation: User ID mismatch:", { identity: identity.subject, requested: args.clerkUserId });
        return {
          user: null,
          created: false,
          authError: true,
          error: "Bruker-ID stemmer ikke overens"
        };
      }

      // Check if user record already exists
      const existingUser = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
        .first();

      if (existingUser) {
        return {
          user: existingUser,
          created: false,
          authError: false,
          error: null
        };
      }

      // Create new user record
      const userId = await ctx.db.insert("users", {
        clerkUserId: args.clerkUserId,
        contractorCompleted: false,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      const newUser = await ctx.db.get(userId);
      return {
        user: newUser,
        created: true,
        authError: false,
        error: null
      };

    } catch (error) {
      console.error("Safe user creation error:", error);
      return {
        user: null,
        created: false,
        authError: true,
        error: error instanceof Error ? error.message : "Ukjent feil"
      };
    }
  },
});

// Safe version of createContractorCompany with retry-friendly error handling
export const createContractorCompanySafe = mutation({
  args: {
    clerkUserId: v.string(),
    // Company data matching existing customer schema
    name: v.string(),
    contactPerson: v.string(),
    phone: v.optional(v.string()),
    email: v.optional(v.string()),
    streetAddress: v.optional(v.string()),
    postalCode: v.optional(v.string()),
    city: v.optional(v.string()),
    entrance: v.optional(v.string()),
    orgNumber: v.string(),
    notes: v.optional(v.string()),
    // Brønnøysundregisteret data tracking
    brregFetchedAt: v.optional(v.number()),
    brregData: v.optional(v.any()), // Use v.any() to match existing schema flexibility
    useCustomAddress: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    try {
      // Validate authentication with safe error handling
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        console.log("Safe contractor company creation: No identity found for user", args.clerkUserId);
        return {
          success: false,
          company: null,
          authError: true,
          error: "Autentisering ikke fullført. Vennligst vent et øyeblikk og prøv igjen.",
          retryable: true
        };
      }

      // Ensure the requesting user matches the Clerk user ID
      if (identity.subject !== args.clerkUserId) {
        console.log("Safe contractor company creation: User ID mismatch:", { identity: identity.subject, requested: args.clerkUserId });
        return {
          success: false,
          company: null,
          authError: true,
          error: "Bruker-ID stemmer ikke overens. Vennligst logg inn på nytt.",
          retryable: false
        };
      }

      // Get or create user record
      let user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
        .first();

      if (!user) {
        const userId = await ctx.db.insert("users", {
          clerkUserId: args.clerkUserId,
          contractorCompleted: false,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
        user = await ctx.db.get(userId);
      }

      // Check if user already has a contractor company
      if (user!.contractorCompanyId) {
        const existingCompany = await ctx.db.get(user!.contractorCompanyId);
        if (existingCompany) {
          return {
            success: true,
            company: existingCompany,
            authError: false,
            error: null,
            retryable: false
          };
        }
      }

      // Clean organization number
      const cleanOrgNumber = args.orgNumber.replace(/\s/g, '');

      // Check for duplicate organization numbers
      const existingCompany = await ctx.db
        .query("customers")
        .withIndex("by_org_number", (q) => q.eq("orgNumber", cleanOrgNumber))
        .first();

      if (existingCompany && existingCompany.contractorUserId !== args.clerkUserId) {
        return {
          success: false,
          company: null,
          authError: false,
          error: "Dette organisasjonsnummeret er allerede registrert av en annen entreprenør",
          retryable: false
        };
      }

      // Create contractor company
      const companyId = await ctx.db.insert("customers", {
        type: "bedrift",
        name: args.name.trim(),
        contactPerson: args.contactPerson.trim(),
        phone: args.phone?.trim() || undefined,
        email: args.email?.trim() || undefined,
        streetAddress: args.streetAddress?.trim() || undefined,
        postalCode: args.postalCode?.trim() || undefined,
        city: args.city?.trim() || undefined,
        entrance: args.entrance?.trim() || undefined,
        orgNumber: cleanOrgNumber,
        notes: args.notes?.trim() || undefined,
        // Mark as contractor company
        contractorUserId: args.clerkUserId,
        // Brønnøysundregisteret data tracking
        brregFetchedAt: args.brregFetchedAt || undefined,
        brregData: args.brregData || undefined,
        useCustomAddress: args.useCustomAddress || undefined,
        // Use contractor's Clerk ID as userId for consistency
        userId: args.clerkUserId,
        createdAt: Date.now(),
      });

      // Update user record with contractor company reference and completion status
      await ctx.db.patch(user!._id, {
        contractorCompanyId: companyId,
        contractorCompleted: true,
        updatedAt: Date.now(),
      });

      // Return the created company
      const createdCompany = await ctx.db.get(companyId);
      return {
        success: true,
        company: createdCompany,
        authError: false,
        error: null,
        retryable: false
      };

    } catch (error) {
      console.error("Safe contractor company creation error:", error);
      return {
        success: false,
        company: null,
        authError: true,
        error: error instanceof Error ? error.message : "En feil oppstod ved registrering av bedriften",
        retryable: true
      };
    }
  },
});
