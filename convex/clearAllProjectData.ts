import { mutation } from './_generated/server';
import { v } from 'convex/values';

/**
 * DEVELOPMENT UTILITY: Comprehensive Database Reset
 *
 * This script safely deletes all user-generated data from the Convex database
 * while preserving the database schema structure and system integrity.
 *
 * ⚠️  WARNING: This is a destructive operation that cannot be undone!
 * ⚠️  Only use in development/testing environments!
 *
 * WHAT GETS DELETED (in correct dependency order):
 * - All typing indicators from the `typingIndicators` table
 * - All chat messages and reactions from the `messages` table
 * - All image likes and customer session data from the `imageLikes` table
 * - All log entries and associated images from the `logEntries` table
 * - All projects (including archived ones) and job data from the `projects` table
 * - All customer records (both regular customers and contractor companies) from the `customers` table
 * - All user records from the `users` table (contractor onboarding data)
 * - All file storage references (images/attachments from Convex storage)
 *
 * WHAT IS PRESERVED:
 * - Database schema definitions and indexes
 * - System configuration and settings
 * - Clerk authentication system (external to Convex)
 *
 * SAFETY FEATURES:
 * - Dry-run option to preview deletions without executing
 * - Comprehensive logging of all operations
 * - Error handling with rollback information
 * - Confirmation code requirement
 * - Foreign key relationship respect
 *
 * USAGE:
 * From Convex Dashboard:
 * 1. Go to your project dashboard
 * 2. Navigate to "Functions" tab
 * 3. Find "clearAllProjectData" or "comprehensiveReset" mutation
 * 4. Execute with: { confirmationCode: "DELETE_ALL_PROJECT_DATA", dryRun: false }
 *
 * From CLI:
 * npx convex run clearAllProjectData:comprehensiveReset '{"confirmationCode": "DELETE_ALL_PROJECT_DATA", "dryRun": false}'
 */
// Legacy function for backward compatibility - simplified to avoid circular reference
export const clearAllProjectData = mutation({
  args: {
    confirmationCode: v.string(),
    environment: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    // Safety check: Require exact confirmation code
    if (args.confirmationCode !== "DELETE_ALL_PROJECT_DATA") {
      throw new Error(
        "❌ Invalid confirmation code. To proceed, use: { confirmationCode: 'DELETE_ALL_PROJECT_DATA' }"
      );
    }

    // Environment safety check
    if (args.environment === "production") {
      throw new Error("❌ This operation is not allowed in production environment!");
    }

    console.log("🚨 LEGACY FUNCTION: Redirecting to comprehensive reset...");
    console.log("💡 Consider using 'comprehensiveReset' function directly for more options");

    // Execute the same logic as comprehensive reset but inline to avoid circular reference
    console.log(`🚨 STARTING COMPREHENSIVE DATABASE RESET...`);
    console.log("⚠️  This operation cannot be undone!");

    const deletionResults = {
      typingIndicators: 0,
      messages: 0,
      imageLikes: 0,
      logEntries: 0,
      projects: 0,
      customers: 0,
      users: 0,
      fileStorageItems: 0,
      totalDeleted: 0,
      errors: [] as string[]
    };

    try {
      // Execute the same deletion logic as comprehensiveReset
      // (This is a simplified version for legacy compatibility)

      // 1. Delete typing indicators
      console.log("🔄 Deleting typing indicators...");
      const typingIndicators = await ctx.db.query("typingIndicators").collect();
      for (const indicator of typingIndicators) {
        await ctx.db.delete(indicator._id);
        deletionResults.typingIndicators++;
      }
      console.log(`✅ Deleted ${deletionResults.typingIndicators} typing indicators`);

      // 2. Delete chat messages
      console.log("🔄 Deleting chat messages...");
      const messages = await ctx.db.query("messages").collect();
      for (const message of messages) {
        await ctx.db.delete(message._id);
        deletionResults.messages++;
      }
      console.log(`✅ Deleted ${deletionResults.messages} chat messages`);

      // 3. Delete image likes
      console.log("🔄 Deleting image likes...");
      const imageLikes = await ctx.db.query("imageLikes").collect();
      for (const like of imageLikes) {
        await ctx.db.delete(like._id);
        deletionResults.imageLikes++;
      }
      console.log(`✅ Deleted ${deletionResults.imageLikes} image likes`);

      // 4. Delete log entries
      console.log("🔄 Deleting log entries...");
      const logEntries = await ctx.db.query("logEntries").collect();
      for (const entry of logEntries) {
        await ctx.db.delete(entry._id);
        deletionResults.logEntries++;
      }
      console.log(`✅ Deleted ${deletionResults.logEntries} log entries`);

      // 5. Delete projects
      console.log("🔄 Deleting projects...");
      const projects = await ctx.db.query("projects").collect();
      for (const project of projects) {
        await ctx.db.delete(project._id);
        deletionResults.projects++;
      }
      console.log(`✅ Deleted ${deletionResults.projects} projects`);

      // 6. Delete customers
      console.log("🔄 Deleting customers...");
      const customers = await ctx.db.query("customers").collect();
      for (const customer of customers) {
        await ctx.db.delete(customer._id);
        deletionResults.customers++;
      }
      console.log(`✅ Deleted ${deletionResults.customers} customers`);

      // 7. Delete users (if table exists)
      console.log("🔄 Deleting user records...");
      try {
        const users = await ctx.db.query("users").collect();
        for (const user of users) {
          await ctx.db.delete(user._id);
          deletionResults.users++;
        }
        console.log(`✅ Deleted ${deletionResults.users} user records`);
      } catch (error) {
        console.log("ℹ️  Users table not found or empty (this is normal for older databases)");
      }

      // Calculate total
      deletionResults.totalDeleted =
        deletionResults.typingIndicators +
        deletionResults.messages +
        deletionResults.imageLikes +
        deletionResults.logEntries +
        deletionResults.projects +
        deletionResults.customers +
        deletionResults.users;

      console.log("🎉 LEGACY DATABASE RESET COMPLETED SUCCESSFULLY!");
      console.log("📊 DELETION SUMMARY:");
      console.log(`   • Typing Indicators: ${deletionResults.typingIndicators}`);
      console.log(`   • Chat Messages: ${deletionResults.messages}`);
      console.log(`   • Image Likes: ${deletionResults.imageLikes}`);
      console.log(`   • Log Entries: ${deletionResults.logEntries}`);
      console.log(`   • Projects: ${deletionResults.projects}`);
      console.log(`   • Customers: ${deletionResults.customers}`);
      console.log(`   • User Records: ${deletionResults.users}`);
      console.log(`   • TOTAL DELETED: ${deletionResults.totalDeleted} records`);
      console.log("✅ Database is now clean for fresh testing!");

      return {
        success: true,
        message: "All project data has been successfully deleted (legacy function)",
        deletionResults,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      deletionResults.errors.push(errorMessage);

      console.error("❌ ERROR DURING LEGACY DELETION PROCESS:", errorMessage);
      console.log("📊 PARTIAL DELETION RESULTS:");
      console.log(`   • Typing Indicators: ${deletionResults.typingIndicators}`);
      console.log(`   • Chat Messages: ${deletionResults.messages}`);
      console.log(`   • Image Likes: ${deletionResults.imageLikes}`);
      console.log(`   • Log Entries: ${deletionResults.logEntries}`);
      console.log(`   • Projects: ${deletionResults.projects}`);
      console.log(`   • Customers: ${deletionResults.customers}`);
      console.log(`   • User Records: ${deletionResults.users}`);

      throw new Error(`Legacy deletion process failed: ${errorMessage}. Partial results: ${JSON.stringify(deletionResults)}`);
    }
  }
});

/**
 * COMPREHENSIVE DATABASE RESET
 *
 * Enhanced version with contractor onboarding support, dry-run capability,
 * and comprehensive safety features.
 */
export const comprehensiveReset = mutation({
  args: {
    confirmationCode: v.string(),
    dryRun: v.optional(v.boolean()), // Preview mode - shows what would be deleted
    includeFileStorage: v.optional(v.boolean()), // Whether to delete files from storage
    environment: v.optional(v.string()) // Environment check
  },
  handler: async (ctx, args) => {
    // Safety check: Require exact confirmation code
    if (args.confirmationCode !== "DELETE_ALL_PROJECT_DATA") {
      throw new Error(
        "❌ Invalid confirmation code. To proceed, use: { confirmationCode: 'DELETE_ALL_PROJECT_DATA' }"
      );
    }

    // Environment safety check
    if (args.environment === "production") {
      throw new Error("❌ This operation is not allowed in production environment!");
    }

    const isDryRun = args.dryRun ?? false;
    const includeFileStorage = args.includeFileStorage ?? false;

    console.log(`🚨 STARTING ${isDryRun ? 'DRY-RUN' : 'COMPREHENSIVE'} DATABASE RESET...`);
    console.log("⚠️  This operation cannot be undone!");
    if (isDryRun) {
      console.log("🔍 DRY-RUN MODE: No data will actually be deleted");
    }

    const deletionResults = {
      typingIndicators: 0,
      messages: 0,
      imageLikes: 0,
      logEntries: 0,
      projects: 0,
      customers: 0,
      users: 0,
      fileStorageItems: 0,
      totalDeleted: 0,
      errors: [] as string[]
    };

    try {
      // 1. Delete typing indicators (no dependencies)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} typing indicators...`);
      const typingIndicators = await ctx.db.query("typingIndicators").collect();
      deletionResults.typingIndicators = typingIndicators.length;

      if (!isDryRun) {
        for (const indicator of typingIndicators) {
          await ctx.db.delete(indicator._id);
        }
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.typingIndicators} typing indicators`);

      // 2. Delete chat messages and reactions (depends on logEntries)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} chat messages and reactions...`);
      const messages = await ctx.db.query("messages").collect();
      deletionResults.messages = messages.length;

      if (!isDryRun) {
        for (const message of messages) {
          await ctx.db.delete(message._id);
        }
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.messages} chat messages`);

      // 3. Delete image likes and customer session data (depends on logEntries and projects)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} image likes and customer sessions...`);
      const imageLikes = await ctx.db.query("imageLikes").collect();
      deletionResults.imageLikes = imageLikes.length;

      if (!isDryRun) {
        for (const like of imageLikes) {
          await ctx.db.delete(like._id);
        }
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.imageLikes} image likes`);

      // 4. Delete log entries and associated images (depends on projects)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} log entries and associated images...`);
      const logEntries = await ctx.db.query("logEntries").collect();
      deletionResults.logEntries = logEntries.length;

      if (!isDryRun) {
        for (const entry of logEntries) {
          // Delete associated image from storage if requested
          if (includeFileStorage && entry.imageId) {
            try {
              await ctx.storage.delete(entry.imageId);
              deletionResults.fileStorageItems++;
            } catch (storageError) {
              console.warn(`⚠️  Could not delete image ${entry.imageId}: ${storageError}`);
            }
          }
          await ctx.db.delete(entry._id);
        }
      } else if (includeFileStorage) {
        // Count images that would be deleted in dry-run
        const imagesCount = logEntries.filter(entry => entry.imageId).length;
        deletionResults.fileStorageItems = imagesCount;
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.logEntries} log entries`);
      if (includeFileStorage) {
        console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.fileStorageItems} associated images`);
      }

      // 5. Delete projects including archived ones and job data (depends on customers)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} projects (including archived) and job data...`);
      const projects = await ctx.db.query("projects").collect();
      deletionResults.projects = projects.length;

      if (!isDryRun) {
        for (const project of projects) {
          // Delete job photos from storage if requested
          if (includeFileStorage && project.jobData?.photos) {
            for (const photo of project.jobData.photos) {
              try {
                // Extract storage ID from URL if it's a Convex storage URL
                const storageId = extractStorageIdFromUrl(photo.url);
                if (storageId) {
                  await ctx.storage.delete(storageId);
                  deletionResults.fileStorageItems++;
                }
              } catch (storageError) {
                console.warn(`⚠️  Could not delete job photo ${photo.url}: ${storageError}`);
              }
            }
          }
          await ctx.db.delete(project._id);
        }
      } else if (includeFileStorage) {
        // Count job photos that would be deleted in dry-run
        let jobPhotosCount = 0;
        for (const project of projects) {
          if (project.jobData?.photos) {
            jobPhotosCount += project.jobData.photos.length;
          }
        }
        deletionResults.fileStorageItems += jobPhotosCount;
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.projects} projects`);

      // 6. Delete customers (both regular customers and contractor companies)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} customers (including contractor companies)...`);
      const customers = await ctx.db.query("customers").collect();
      deletionResults.customers = customers.length;

      if (!isDryRun) {
        for (const customer of customers) {
          await ctx.db.delete(customer._id);
        }
      }

      // Count contractor vs regular customers for detailed reporting
      const contractorCustomers = customers.filter(c => c.contractorUserId);
      const regularCustomers = customers.filter(c => !c.contractorUserId);
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.customers} customers (${contractorCustomers.length} contractor companies, ${regularCustomers.length} regular customers)`);

      // 7. Delete user records (contractor onboarding data)
      console.log(`🔄 ${isDryRun ? 'Analyzing' : 'Deleting'} user records (contractor onboarding data)...`);
      const users = await ctx.db.query("users").collect();
      deletionResults.users = users.length;

      if (!isDryRun) {
        for (const user of users) {
          await ctx.db.delete(user._id);
        }
      }
      console.log(`${isDryRun ? '📊' : '✅'} ${isDryRun ? 'Found' : 'Deleted'} ${deletionResults.users} user records`);

      // Calculate total
      deletionResults.totalDeleted =
        deletionResults.typingIndicators +
        deletionResults.messages +
        deletionResults.imageLikes +
        deletionResults.logEntries +
        deletionResults.projects +
        deletionResults.customers +
        deletionResults.users +
        deletionResults.fileStorageItems;

      const operationVerb = isDryRun ? 'ANALYSIS' : 'DELETION';
      const operationPastTense = isDryRun ? 'analyzed' : 'deleted';

      console.log(`🎉 COMPREHENSIVE DATABASE ${operationVerb} COMPLETED SUCCESSFULLY!`);
      console.log(`📊 ${operationVerb} SUMMARY:`);
      console.log(`   • Typing Indicators: ${deletionResults.typingIndicators}`);
      console.log(`   • Chat Messages: ${deletionResults.messages}`);
      console.log(`   • Image Likes: ${deletionResults.imageLikes}`);
      console.log(`   • Log Entries: ${deletionResults.logEntries}`);
      console.log(`   • Projects: ${deletionResults.projects}`);
      console.log(`   • Customers: ${deletionResults.customers}`);
      console.log(`   • User Records: ${deletionResults.users}`);
      if (includeFileStorage) {
        console.log(`   • File Storage Items: ${deletionResults.fileStorageItems}`);
      }
      console.log(`   • TOTAL ${operationPastTense.toUpperCase()}: ${deletionResults.totalDeleted} records`);

      if (isDryRun) {
        console.log("🔍 DRY-RUN COMPLETE: No data was actually deleted");
        console.log("💡 To execute the deletion, run again with dryRun: false");
      } else {
        console.log("✅ Database is now completely clean for fresh testing!");
        console.log("🔄 All schema structures and indexes remain intact");
      }

      return {
        success: true,
        message: isDryRun
          ? "Dry-run analysis completed successfully"
          : "All user data has been successfully deleted",
        isDryRun,
        includeFileStorage,
        deletionResults,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      deletionResults.errors.push(errorMessage);

      const operationVerb = isDryRun ? 'ANALYSIS' : 'DELETION';
      console.error(`❌ ERROR DURING ${operationVerb} PROCESS:`, errorMessage);
      console.log(`📊 PARTIAL ${operationVerb} RESULTS:`);
      console.log(`   • Typing Indicators: ${deletionResults.typingIndicators}`);
      console.log(`   • Chat Messages: ${deletionResults.messages}`);
      console.log(`   • Image Likes: ${deletionResults.imageLikes}`);
      console.log(`   • Log Entries: ${deletionResults.logEntries}`);
      console.log(`   • Projects: ${deletionResults.projects}`);
      console.log(`   • Customers: ${deletionResults.customers}`);
      console.log(`   • User Records: ${deletionResults.users}`);
      if (includeFileStorage) {
        console.log(`   • File Storage Items: ${deletionResults.fileStorageItems}`);
      }

      if (!isDryRun) {
        console.log("⚠️  IMPORTANT: Some data may have been partially deleted!");
        console.log("⚠️  Database may be in an inconsistent state!");
        console.log("💡 Consider running the operation again to complete the cleanup");
      }

      throw new Error(`${operationVerb} process failed: ${errorMessage}. Partial results: ${JSON.stringify(deletionResults)}`);
    }
  }
});

/**
 * Helper function to extract Convex storage ID from URL
 * Convex storage URLs typically follow the pattern: https://domain.convex.cloud/api/storage/[storageId]
 */
function extractStorageIdFromUrl(url: string): string | null {
  try {
    const match = url.match(/\/api\/storage\/([^/?]+)/);
    return match ? match[1] : null;
  } catch {
    return null;
  }
}

/**
 * DEVELOPMENT UTILITY: Get Comprehensive Data Count
 *
 * This query returns the current count of all user-generated data
 * without deleting anything. Useful for checking database state
 * before and after running the reset script.
 *
 * Enhanced to include contractor onboarding data and detailed breakdowns.
 */
export const getProjectDataCount = mutation({
  args: {},
  handler: async (ctx) => {
    try {
      const counts = {
        typingIndicators: 0,
        messages: 0,
        imageLikes: 0,
        logEntries: 0,
        projects: 0,
        customers: 0,
        users: 0,
        total: 0,
        // Detailed breakdowns
        archivedProjects: 0,
        contractorCustomers: 0,
        regularCustomers: 0,
        completedOnboarding: 0,
        pendingOnboarding: 0
      };

      // Count all user-generated data
      counts.typingIndicators = (await ctx.db.query("typingIndicators").collect()).length;
      counts.messages = (await ctx.db.query("messages").collect()).length;
      counts.imageLikes = (await ctx.db.query("imageLikes").collect()).length;

      const logEntries = await ctx.db.query("logEntries").collect();
      counts.logEntries = logEntries.length;

      const projects = await ctx.db.query("projects").collect();
      counts.projects = projects.length;
      counts.archivedProjects = projects.filter(p => p.isArchived).length;

      const customers = await ctx.db.query("customers").collect();
      counts.customers = customers.length;
      counts.contractorCustomers = customers.filter(c => c.contractorUserId).length;
      counts.regularCustomers = customers.filter(c => !c.contractorUserId).length;

      const users = await ctx.db.query("users").collect();
      counts.users = users.length;
      counts.completedOnboarding = users.filter(u => u.contractorCompleted).length;
      counts.pendingOnboarding = users.filter(u => !u.contractorCompleted).length;

      counts.total = counts.typingIndicators + counts.messages + counts.imageLikes +
                    counts.logEntries + counts.projects + counts.customers + counts.users;

      console.log("📊 COMPREHENSIVE DATABASE STATE:");
      console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      console.log("📱 CHAT SYSTEM:");
      console.log(`   • Typing Indicators: ${counts.typingIndicators}`);
      console.log(`   • Chat Messages: ${counts.messages}`);
      console.log(`   • Image Likes: ${counts.imageLikes}`);
      console.log("");
      console.log("📋 PROJECT SYSTEM:");
      console.log(`   • Log Entries: ${counts.logEntries}`);
      console.log(`   • Projects: ${counts.projects} (${counts.archivedProjects} archived, ${counts.projects - counts.archivedProjects} active)`);
      console.log("");
      console.log("👥 USER MANAGEMENT:");
      console.log(`   • User Records: ${counts.users} (${counts.completedOnboarding} completed onboarding, ${counts.pendingOnboarding} pending)`);
      console.log(`   • Customers: ${counts.customers} (${counts.contractorCustomers} contractor companies, ${counts.regularCustomers} regular customers)`);
      console.log("");
      console.log(`🔢 TOTAL RECORDS: ${counts.total}`);
      console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

      return {
        success: true,
        counts,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.error("❌ ERROR GETTING DATA COUNT:", errorMessage);
      throw new Error(`Failed to get data count: ${errorMessage}`);
    }
  }
});

/**
 * DEVELOPMENT UTILITY: Dry-Run Database Reset
 *
 * Convenience function that runs a dry-run analysis without actually deleting data.
 * Shows exactly what would be deleted if the full reset was executed.
 */
export const dryRunReset = mutation({
  args: {
    includeFileStorage: v.optional(v.boolean())
  },
  handler: async (ctx, args) => {
    // Call comprehensive reset with dry-run enabled
    const includeFileStorage = args.includeFileStorage ?? false;

    console.log("🔍 STARTING DRY-RUN ANALYSIS...");
    console.log("📊 This will show what would be deleted without actually deleting anything");

    const deletionResults = {
      typingIndicators: 0,
      messages: 0,
      imageLikes: 0,
      logEntries: 0,
      projects: 0,
      customers: 0,
      users: 0,
      fileStorageItems: 0,
      totalDeleted: 0,
      errors: [] as string[]
    };

    try {
      // Analyze typing indicators
      const typingIndicators = await ctx.db.query("typingIndicators").collect();
      deletionResults.typingIndicators = typingIndicators.length;
      console.log(`📊 Found ${deletionResults.typingIndicators} typing indicators`);

      // Analyze chat messages
      const messages = await ctx.db.query("messages").collect();
      deletionResults.messages = messages.length;
      console.log(`📊 Found ${deletionResults.messages} chat messages`);

      // Analyze image likes
      const imageLikes = await ctx.db.query("imageLikes").collect();
      deletionResults.imageLikes = imageLikes.length;
      console.log(`📊 Found ${deletionResults.imageLikes} image likes`);

      // Analyze log entries
      const logEntries = await ctx.db.query("logEntries").collect();
      deletionResults.logEntries = logEntries.length;
      if (includeFileStorage) {
        const imagesCount = logEntries.filter(entry => entry.imageId).length;
        deletionResults.fileStorageItems += imagesCount;
      }
      console.log(`📊 Found ${deletionResults.logEntries} log entries`);

      // Analyze projects
      const projects = await ctx.db.query("projects").collect();
      deletionResults.projects = projects.length;
      if (includeFileStorage) {
        let jobPhotosCount = 0;
        for (const project of projects) {
          if (project.jobData?.photos) {
            jobPhotosCount += project.jobData.photos.length;
          }
        }
        deletionResults.fileStorageItems += jobPhotosCount;
      }
      const archivedCount = projects.filter(p => p.isArchived).length;
      console.log(`📊 Found ${deletionResults.projects} projects (${archivedCount} archived, ${deletionResults.projects - archivedCount} active)`);

      // Analyze customers
      const customers = await ctx.db.query("customers").collect();
      deletionResults.customers = customers.length;
      const contractorCustomers = customers.filter(c => c.contractorUserId);
      const regularCustomers = customers.filter(c => !c.contractorUserId);
      console.log(`📊 Found ${deletionResults.customers} customers (${contractorCustomers.length} contractor companies, ${regularCustomers.length} regular customers)`);

      // Analyze users
      try {
        const users = await ctx.db.query("users").collect();
        deletionResults.users = users.length;
        const completedOnboarding = users.filter(u => u.contractorCompleted).length;
        console.log(`📊 Found ${deletionResults.users} user records (${completedOnboarding} completed onboarding, ${deletionResults.users - completedOnboarding} pending)`);
      } catch (error) {
        console.log("ℹ️  Users table not found (this is normal for older databases)");
      }

      if (includeFileStorage) {
        console.log(`📊 Found ${deletionResults.fileStorageItems} file storage items`);
      }

      // Calculate total
      deletionResults.totalDeleted =
        deletionResults.typingIndicators +
        deletionResults.messages +
        deletionResults.imageLikes +
        deletionResults.logEntries +
        deletionResults.projects +
        deletionResults.customers +
        deletionResults.users +
        deletionResults.fileStorageItems;

      console.log(`📊 TOTAL ANALYZED: ${deletionResults.totalDeleted} records`);
      console.log("🔍 DRY-RUN COMPLETE: No data was actually deleted");
      console.log("💡 To execute the deletion, use comprehensiveReset with dryRun: false");

      return {
        success: true,
        message: "Dry-run analysis completed successfully",
        isDryRun: true,
        includeFileStorage,
        deletionResults,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      deletionResults.errors.push(errorMessage);

      console.error("❌ ERROR DURING DRY-RUN ANALYSIS:", errorMessage);
      throw new Error(`Dry-run analysis failed: ${errorMessage}`);
    }
  }
});
