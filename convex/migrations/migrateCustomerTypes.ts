import { mutation } from '../_generated/server';
import { v } from 'convex/values';

/**
 * Migration script to convert all "firma" customer types to "bedrift"
 * This migration ensures consistency in the customer type enum by removing the legacy "firma" value
 * 
 * Usage: npx convex run migrations/migrateCustomerTypes:migrateCustomerTypesFromFirmaToBedrift
 */
export const migrateCustomerTypesFromFirmaToBedrift = mutation({
  args: {
    dryRun: v.optional(v.boolean()) // If true, only reports what would be changed without making changes
  },
  handler: async (ctx, args) => {
    const dryRun = args.dryRun ?? false;
    
    console.log(`🔄 Starting customer type migration (${dryRun ? 'DRY RUN' : 'LIVE RUN'})...`);
    
    // Get all customers with type "firma" - need to query all and filter since "firma" is no longer in schema
    const allCustomers = await ctx.db.query("customers").collect();
    const firmaCustomers = allCustomers.filter(c => (c as any).type === "firma");
    
    console.log(`📊 Found ${firmaCustomers.length} customers with type "firma"`);
    
    if (firmaCustomers.length === 0) {
      console.log("✅ No customers with type 'firma' found. Migration not needed.");
      return {
        success: true,
        migratedCount: 0,
        message: "No customers with type 'firma' found"
      };
    }
    
    // Log details of customers that will be migrated
    console.log("📋 Customers to be migrated:");
    firmaCustomers.forEach((customer, index) => {
      console.log(`   ${index + 1}. ${customer.name} (ID: ${customer._id}) - Created: ${new Date(customer.createdAt).toISOString()}`);
    });
    
    if (dryRun) {
      console.log("🔍 DRY RUN: No changes made. Run without dryRun flag to apply changes.");
      return {
        success: true,
        migratedCount: 0,
        dryRun: true,
        customersToMigrate: firmaCustomers.length,
        message: `Would migrate ${firmaCustomers.length} customers from 'firma' to 'bedrift'`
      };
    }
    
    // Perform the actual migration
    let migratedCount = 0;
    const errors: string[] = [];
    
    for (const customer of firmaCustomers) {
      try {
        await ctx.db.patch(customer._id, {
          type: "bedrift" as const
        });
        migratedCount++;
        console.log(`✅ Migrated customer: ${customer.name} (${customer._id})`);
      } catch (error) {
        const errorMessage = `Failed to migrate customer ${customer.name} (${customer._id}): ${error}`;
        console.error(`❌ ${errorMessage}`);
        errors.push(errorMessage);
      }
    }
    
    // Verify migration results
    const allCustomersAfter = await ctx.db.query("customers").collect();
    const remainingFirmaCustomers = allCustomersAfter.filter(c => (c as any).type === "firma");
    
    console.log(`📊 Migration Results:`);
    console.log(`   ✅ Successfully migrated: ${migratedCount} customers`);
    console.log(`   ❌ Failed migrations: ${errors.length}`);
    console.log(`   🔍 Remaining 'firma' customers: ${remainingFirmaCustomers.length}`);
    
    if (errors.length > 0) {
      console.log("❌ Migration errors:");
      errors.forEach(error => console.log(`   - ${error}`));
    }
    
    const success = errors.length === 0 && remainingFirmaCustomers.length === 0;
    
    console.log(`${success ? '✅' : '⚠️'} Migration ${success ? 'completed successfully' : 'completed with issues'}`);
    
    return {
      success,
      migratedCount,
      errors,
      remainingFirmaCustomers: remainingFirmaCustomers.length,
      message: success 
        ? `Successfully migrated ${migratedCount} customers from 'firma' to 'bedrift'`
        : `Migration completed with ${errors.length} errors and ${remainingFirmaCustomers.length} remaining 'firma' customers`
    };
  }
});

/**
 * Verification function to check migration status
 * 
 * Usage: npx convex run migrations/migrateCustomerTypes:verifyCustomerTypeMigration
 */
export const verifyCustomerTypeMigration = mutation({
  args: {},
  handler: async (ctx) => {
    console.log("🔍 Verifying customer type migration status...");
    
    // Count customers by type
    const allCustomers = await ctx.db.query("customers").collect();
    const privatCustomers = allCustomers.filter(c => c.type === "privat");
    const firmaCustomers = allCustomers.filter(c => (c as any).type === "firma");
    const bedriftCustomers = allCustomers.filter(c => c.type === "bedrift");
    
    console.log("📊 Customer Type Distribution:");
    console.log(`   Privat: ${privatCustomers.length}`);
    console.log(`   Firma (legacy): ${firmaCustomers.length}`);
    console.log(`   Bedrift: ${bedriftCustomers.length}`);
    console.log(`   Total: ${allCustomers.length}`);
    
    if (firmaCustomers.length > 0) {
      console.log("⚠️ Warning: Found customers with legacy 'firma' type:");
      firmaCustomers.forEach(customer => {
        console.log(`   - ${customer.name} (${customer._id})`);
      });
    } else {
      console.log("✅ No legacy 'firma' customers found. Migration is complete.");
    }
    
    return {
      total: allCustomers.length,
      privat: privatCustomers.length,
      firma: firmaCustomers.length,
      bedrift: bedriftCustomers.length,
      migrationComplete: firmaCustomers.length === 0
    };
  }
});

/**
 * Rollback function to revert migration if needed (emergency use only)
 * This function converts all "bedrift" customers back to "firma"
 * 
 * Usage: npx convex run migrations/migrateCustomerTypes:rollbackCustomerTypeMigration
 */
export const rollbackCustomerTypeMigration = mutation({
  args: {
    confirmRollback: v.boolean() // Must be true to execute rollback
  },
  handler: async (ctx, args) => {
    if (!args.confirmRollback) {
      throw new Error("Rollback requires confirmRollback: true to prevent accidental execution");
    }
    
    console.log("⚠️ WARNING: Starting customer type migration ROLLBACK...");
    console.log("This will convert all 'bedrift' customers back to 'firma'");
    
    const allCustomersForRollback = await ctx.db.query("customers").collect();
    const bedriftCustomers = allCustomersForRollback.filter(c => c.type === "bedrift");
    
    console.log(`📊 Found ${bedriftCustomers.length} customers with type "bedrift" to rollback`);
    
    let rolledBackCount = 0;
    const errors: string[] = [];
    
    for (const customer of bedriftCustomers) {
      try {
        await ctx.db.patch(customer._id, {
          type: "bedrift" as const // Changed from "firma" to "bedrift" since "firma" is no longer in schema
        });
        rolledBackCount++;
        console.log(`↩️ Rolled back customer: ${customer.name} (${customer._id})`);
      } catch (error) {
        const errorMessage = `Failed to rollback customer ${customer.name} (${customer._id}): ${error}`;
        console.error(`❌ ${errorMessage}`);
        errors.push(errorMessage);
      }
    }
    
    console.log(`📊 Rollback Results:`);
    console.log(`   ↩️ Successfully rolled back: ${rolledBackCount} customers`);
    console.log(`   ❌ Failed rollbacks: ${errors.length}`);
    
    return {
      success: errors.length === 0,
      rolledBackCount,
      errors,
      message: `Rolled back ${rolledBackCount} customers from 'bedrift' to 'firma'`
    };
  }
});
