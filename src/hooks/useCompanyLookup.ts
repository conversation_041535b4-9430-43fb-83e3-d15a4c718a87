import React, { useState, useCallback, useRef } from 'react';
import { searchCompanies, getCompanyByOrgNumber, CompanyInfo, CompanyLookupError } from '../services/companyLookup';

export interface UseCompanyLookupState {
  /** Current search results */
  results: CompanyInfo[];
  /** Loading state */
  isLoading: boolean;
  /** Error state */
  error: CompanyLookupError | null;
  /** Whether search has been performed */
  hasSearched: boolean;
  /** Selected company from results */
  selectedCompany: CompanyInfo | null;
}

export interface UseCompanyLookupActions {
  /** Search for companies by name */
  searchByName: (query: string) => Promise<void>;
  /** Lookup company by organization number */
  lookupByOrgNumber: (orgNumber: string) => Promise<void>;
  /** Select a company from search results */
  selectCompany: (company: CompanyInfo) => void;
  /** Clear search results and state */
  clearResults: () => void;
  /** Clear error state */
  clearError: () => void;
}

export interface UseCompanyLookupReturn extends UseCompanyLookupState, UseCompanyLookupActions {}

/**
 * Custom hook for managing company lookup functionality
 * Provides search, selection, and state management for Norwegian company data
 * 
 * @example
 * ```tsx
 * const {
 *   results,
 *   isLoading,
 *   error,
 *   selectedCompany,
 *   searchByName,
 *   selectCompany,
 *   clearResults
 * } = useCompanyLookup();
 * 
 * // Search for companies
 * await searchByName('Equinor');
 * 
 * // Select a company and auto-fill form
 * selectCompany(results[0]);
 * ```
 */
export function useCompanyLookup(): UseCompanyLookupReturn {
  const [state, setState] = useState<UseCompanyLookupState>({
    results: [],
    isLoading: false,
    error: null,
    hasSearched: false,
    selectedCompany: null
  });

  // Ref to track the latest search request to prevent race conditions
  const searchIdRef = useRef(0);

  const searchByName = useCallback(async (query: string) => {
    if (!query || query.trim().length < 2) {
      setState(prev => ({
        ...prev,
        results: [],
        error: null,
        hasSearched: false
      }));
      return;
    }

    // Generate unique search ID to handle race conditions
    const searchId = ++searchIdRef.current;

    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
      hasSearched: false
    }));

    try {
      const result = await searchCompanies(query.trim(), 10);

      // Check if this is still the latest search
      if (searchId !== searchIdRef.current) {
        return; // Ignore outdated results
      }

      if (result.success) {
        setState(prev => ({
          ...prev,
          results: result.data.companies,
          isLoading: false,
          error: null,
          hasSearched: true
        }));
      } else {
        setState(prev => ({
          ...prev,
          results: [],
          isLoading: false,
          error: result.error,
          hasSearched: true
        }));
      }
    } catch (error) {
      // Check if this is still the latest search
      if (searchId !== searchIdRef.current) {
        return;
      }

      setState(prev => ({
        ...prev,
        results: [],
        isLoading: false,
        error: {
          code: 'NETWORK_ERROR',
          message: 'Uventet feil ved søk etter firma',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        hasSearched: true
      }));
    }
  }, []);

  const lookupByOrgNumber = useCallback(async (orgNumber: string) => {
    if (!orgNumber || orgNumber.trim().length === 0) {
      return;
    }

    const searchId = ++searchIdRef.current;

    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
      hasSearched: false
    }));

    try {
      const result = await getCompanyByOrgNumber(orgNumber.trim());

      // Check if this is still the latest search
      if (searchId !== searchIdRef.current) {
        return;
      }

      if (result.success) {
        setState(prev => ({
          ...prev,
          results: [result.data],
          selectedCompany: result.data,
          isLoading: false,
          error: null,
          hasSearched: true
        }));
      } else {
        setState(prev => ({
          ...prev,
          results: [],
          selectedCompany: null,
          isLoading: false,
          error: result.error,
          hasSearched: true
        }));
      }
    } catch (error) {
      if (searchId !== searchIdRef.current) {
        return;
      }

      setState(prev => ({
        ...prev,
        results: [],
        selectedCompany: null,
        isLoading: false,
        error: {
          code: 'NETWORK_ERROR',
          message: 'Uventet feil ved oppslag av organisasjonsnummer',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        hasSearched: true
      }));
    }
  }, []);

  const selectCompany = useCallback((company: CompanyInfo) => {
    setState(prev => ({
      ...prev,
      selectedCompany: company,
      error: null
    }));
  }, []);

  const clearResults = useCallback(() => {
    // Cancel any pending searches
    searchIdRef.current++;
    
    setState({
      results: [],
      isLoading: false,
      error: null,
      hasSearched: false,
      selectedCompany: null
    });
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null
    }));
  }, []);

  return {
    ...state,
    searchByName,
    lookupByOrgNumber,
    selectCompany,
    clearResults,
    clearError
  };
}

/**
 * Debounced version of useCompanyLookup for search-as-you-type functionality
 * @param delay Debounce delay in milliseconds (default: 300)
 */
export function useDebouncedCompanyLookup(delay: number = 300): UseCompanyLookupReturn & {
  /** Debounced search function */
  debouncedSearchByName: (query: string) => void;
} {
  const companyLookup = useCompanyLookup();
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();

  const debouncedSearchByName = useCallback((query: string) => {
    // Clear existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set new timeout
    debounceTimeoutRef.current = setTimeout(() => {
      companyLookup.searchByName(query);
    }, delay);
  }, [companyLookup.searchByName, delay]);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    ...companyLookup,
    debouncedSearchByName
  };
}
