/**
 * Company lookup service for Norwegian Business Register (Brønnøysundregisteret)
 * Provides functionality to search for companies and retrieve their information
 */

export interface CompanyInfo {
  /** Company name */
  name: string;
  /** Organization number (9 digits) */
  organizationNumber: string;
  /** Organization form (e.g., "Aksjeselskap", "Enkeltpersonforetak") */
  organizationForm?: string;
  /** Organization form code (e.g., "AS", "ENK") */
  organizationFormCode?: string;
  /** Primary NACE industry code (5-digit code like "41201") */
  naeringskode1?: string;
  /** Primary industry description */
  industryDescription?: string;
  /** Establishment date (company founding date) */
  establishmentDate?: string;
  /** Number of employees (if available) */
  numberOfEmployees?: number;
  /** Contact information from registry */
  registryContact?: {
    phone?: string;
    email?: string;
    /** Source of phone number: 'mobil' for mobile, 'telefon' for landline */
    phoneSource?: 'mobil' | 'telefon';
  };
  /** Visiting address information */
  visitingAddress?: {
    street: string;
    postalCode: string;
    city: string;
    municipality?: string;
  };
  /** Business address (if different from visiting address) */
  businessAddress?: {
    street: string;
    postalCode: string;
    city: string;
    municipality?: string;
  };
  /** Managing director information */
  managingDirector?: {
    firstName: string;
    lastName: string;
    fullName: string;
    birthDate?: string;
    /** Role type: 'DAGL' for Daglig leder, 'INNH' for Innehaver, etc. */
    roleType?: string;
    /** Human-readable role description */
    roleDescription?: string;
  };
  /** Company status */
  status: 'active' | 'inactive' | 'dissolved';
  /** Industry code (legacy field for backward compatibility) */
  industryCode?: string;
}

export interface CompanySearchResult {
  companies: CompanyInfo[];
  totalCount: number;
  hasMore: boolean;
}

export interface CompanyLookupError {
  code: 'NETWORK_ERROR' | 'API_ERROR' | 'NOT_FOUND' | 'INVALID_INPUT' | 'RATE_LIMIT';
  message: string;
  details?: string;
}

/**
 * Search for companies by name using Brønnøysundregisteret API
 * @param query Company name to search for
 * @param limit Maximum number of results to return (default: 10)
 * @returns Promise with search results or error
 */
export async function searchCompanies(
  query: string, 
  limit: number = 10
): Promise<{ success: true; data: CompanySearchResult } | { success: false; error: CompanyLookupError }> {
  try {
    // Validate input
    if (!query || query.trim().length < 2) {
      return {
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Firmanavn må være minst 2 tegn langt'
        }
      };
    }

    const trimmedQuery = query.trim();

    // Use Brønnøysundregisteret's open data API
    // API documentation: https://data.brreg.no/enhetsregisteret/api/docs/index.html
    const apiUrl = new URL('https://data.brreg.no/enhetsregisteret/api/enheter');
    apiUrl.searchParams.set('navn', trimmedQuery);
    apiUrl.searchParams.set('size', limit.toString());
    apiUrl.searchParams.set('page', '0');
    
    const response = await fetch(apiUrl.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'JobbLogg/1.0 (Company Lookup Service)'
      }
    });

    if (!response.ok) {
      if (response.status === 429) {
        return {
          success: false,
          error: {
            code: 'RATE_LIMIT',
            message: 'For mange forespørsler. Prøv igjen om litt.',
            details: `HTTP ${response.status}`
          }
        };
      }
      
      return {
        success: false,
        error: {
          code: 'API_ERROR',
          message: 'Kunne ikke hente firmainformasjon',
          details: `HTTP ${response.status}: ${response.statusText}`
        }
      };
    }

    const data = await response.json();
    
    // Transform API response to our format
    const companies: CompanyInfo[] = await Promise.all(
      (data._embedded?.enheter || []).map(async (enhet: any) => {
        const company: CompanyInfo = {
          name: enhet.navn,
          organizationNumber: enhet.organisasjonsnummer,
          organizationForm: enhet.organisasjonsform?.beskrivelse,
          organizationFormCode: enhet.organisasjonsform?.kode,
          naeringskode1: enhet.naeringskode1?.kode,
          industryDescription: enhet.naeringskode1?.beskrivelse,
          establishmentDate: enhet.stiftelsesdato,
          numberOfEmployees: enhet.antallAnsatte,
          status: mapStatus(enhet.organisasjonsform?.kode, enhet.konkurs, enhet.underAvvikling),
          // Legacy field for backward compatibility
          industryCode: enhet.naeringskode1?.kode
        };

      // Add visiting address if available
      if (enhet.beliggenhetsadresse) {
        const addr = enhet.beliggenhetsadresse;
        company.visitingAddress = {
          street: [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim(),
          postalCode: addr.postnummer || '',
          city: addr.poststed || '',
          municipality: addr.kommune
        };
      }

      // Add business address if available and different
      if (enhet.forretningsadresse && enhet.forretningsadresse !== enhet.beliggenhetsadresse) {
        const addr = enhet.forretningsadresse;
        company.businessAddress = {
          street: [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim(),
          postalCode: addr.postnummer || '',
          city: addr.poststed || '',
          municipality: addr.kommune
        };
      }

      // Add registry contact information if available
      console.log('[CompanyLookup] Registry contact data from API:', {
        telefon: enhet.telefon,
        mobil: enhet.mobil,
        epostadresse: enhet.epostadresse,
        orgNumber: enhet.organisasjonsnummer
      });

      // Prioritize mobile number over landline for phone auto-population
      const phoneNumber = enhet.mobil || enhet.telefon;
      const phoneSource: 'mobil' | 'telefon' | undefined = enhet.mobil ? 'mobil' : (enhet.telefon ? 'telefon' : undefined);

      if (phoneNumber || enhet.epostadresse) {
        company.registryContact = {
          phone: phoneNumber,
          email: enhet.epostadresse,
          phoneSource: phoneSource
        };
        console.log('[CompanyLookup] Created registryContact:', {
          phone: phoneNumber,
          phoneSource: phoneSource,
          email: enhet.epostadresse
        });
      } else {
        console.log('[CompanyLookup] No registry contact data available for:', enhet.organisasjonsnummer);
      }

      // Fetch managing director information (optional)
      try {
        company.managingDirector = await fetchManagingDirector(enhet.organisasjonsnummer);
      } catch (error) {
        // Managing director info is optional for search results
        console.warn('[CompanyLookup] Managing director fetch failed for', enhet.organisasjonsnummer, error);
      }

      return company;
    }));

    return {
      success: true,
      data: {
        companies,
        totalCount: data.page?.totalElements || companies.length,
        hasMore: (data.page?.totalElements || 0) > (data.page?.size || 0)
      }
    };

  } catch (error) {
    console.error('[CompanyLookup] Search failed:', error);
    
    return {
      success: false,
      error: {
        code: 'NETWORK_ERROR',
        message: 'Nettverksfeil. Sjekk internettforbindelsen din.',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

/**
 * Fetch managing director information from roles API
 * @param orgNumber Organization number (9 digits)
 * @returns Managing director info or null if not found
 */
async function fetchManagingDirector(orgNumber: string): Promise<CompanyInfo['managingDirector'] | null> {
  try {
    const apiUrl = `https://data.brreg.no/enhetsregisteret/api/enheter/${orgNumber}/roller`;

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'JobbLogg/1.0 (Managing Director Lookup)'
      }
    });

    if (!response.ok) {
      // Roles API might not be available for all companies
      return null;
    }

    const rolesData = await response.json();

    // Define role types to look for in priority order
    const roleTypesToCheck = [
      { code: 'DAGL', description: 'Daglig leder' },
      { code: 'INNH', description: 'Innehaver' },
      { code: 'BEST', description: 'Bestyrende direktør' },
      { code: 'ADMD', description: 'Administrerende direktør' }
    ];

    // Look for managing person roles in priority order
    for (const roleType of roleTypesToCheck) {
      const roleGroup = rolesData.rollegrupper?.find((group: any) =>
        group.type?.kode === roleType.code
      );

      if (roleGroup?.roller?.[0]?.person) {
        const person = roleGroup.roller[0].person;
        const navn = person.navn;

        return {
          firstName: navn.fornavn || '',
          lastName: navn.etternavn || '',
          fullName: [navn.fornavn, navn.mellomnavn, navn.etternavn].filter(Boolean).join(' '),
          birthDate: person.fodselsdato,
          roleType: roleType.code,
          roleDescription: roleType.description
        };
      }
    }

    return null;
  } catch (error) {
    // Silently fail - managing director info is optional
    console.warn('[CompanyLookup] Failed to fetch managing director:', error);
    return null;
  }
}

/**
 * Get company information by organization number
 * @param orgNumber Organization number (9 digits)
 * @returns Promise with company information or error
 */
export async function getCompanyByOrgNumber(
  orgNumber: string
): Promise<{ success: true; data: CompanyInfo } | { success: false; error: CompanyLookupError }> {
  try {
    // Validate and clean organization number
    const cleanOrgNumber = orgNumber.replace(/\D/g, '');
    if (cleanOrgNumber.length !== 9) {
      return {
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Organisasjonsnummer må være 9 siffer'
        }
      };
    }

    const apiUrl = `https://data.brreg.no/enhetsregisteret/api/enheter/${cleanOrgNumber}`;
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'JobbLogg/1.0 (Company Lookup Service)'
      }
    });

    if (response.status === 404) {
      return {
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Fant ikke firma med dette organisasjonsnummeret'
        }
      };
    }

    if (!response.ok) {
      return {
        success: false,
        error: {
          code: 'API_ERROR',
          message: 'Kunne ikke hente firmainformasjon',
          details: `HTTP ${response.status}: ${response.statusText}`
        }
      };
    }

    const enhet = await response.json();

    // Fetch managing director information in parallel
    const managingDirectorPromise = fetchManagingDirector(cleanOrgNumber);

    // Transform to our format (same logic as search)
    const company: CompanyInfo = {
      name: enhet.navn,
      organizationNumber: enhet.organisasjonsnummer,
      organizationForm: enhet.organisasjonsform?.beskrivelse,
      organizationFormCode: enhet.organisasjonsform?.kode,
      naeringskode1: enhet.naeringskode1?.kode,
      industryDescription: enhet.naeringskode1?.beskrivelse,
      establishmentDate: enhet.stiftelsesdato,
      numberOfEmployees: enhet.antallAnsatte,
      status: mapStatus(enhet.organisasjonsform?.kode, enhet.konkurs, enhet.underAvvikling),
      // Legacy field for backward compatibility
      industryCode: enhet.naeringskode1?.kode
    };

    // Add addresses
    if (enhet.beliggenhetsadresse) {
      const addr = enhet.beliggenhetsadresse;
      company.visitingAddress = {
        street: [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim(),
        postalCode: addr.postnummer || '',
        city: addr.poststed || '',
        municipality: addr.kommune
      };
    }

    if (enhet.forretningsadresse && enhet.forretningsadresse !== enhet.beliggenhetsadresse) {
      const addr = enhet.forretningsadresse;
      company.businessAddress = {
        street: [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim(),
        postalCode: addr.postnummer || '',
        city: addr.poststed || '',
        municipality: addr.kommune
      };
    }

    // Add registry contact information if available
    console.log('[CompanyLookup] Single lookup registry contact data:', {
      telefon: enhet.telefon,
      mobil: enhet.mobil,
      epostadresse: enhet.epostadresse,
      orgNumber: enhet.organisasjonsnummer
    });

    // Prioritize mobile number over landline for phone auto-population
    const phoneNumber = enhet.mobil || enhet.telefon;
    const phoneSource: 'mobil' | 'telefon' | undefined = enhet.mobil ? 'mobil' : (enhet.telefon ? 'telefon' : undefined);

    if (phoneNumber || enhet.epostadresse) {
      company.registryContact = {
        phone: phoneNumber,
        email: enhet.epostadresse,
        phoneSource: phoneSource
      };
      console.log('[CompanyLookup] Single lookup created registryContact:', {
        phone: phoneNumber,
        phoneSource: phoneSource,
        email: enhet.epostadresse
      });
    } else {
      console.log('[CompanyLookup] Single lookup - no registry contact data for:', enhet.organisasjonsnummer);
    }

    // Wait for managing director information
    try {
      company.managingDirector = await managingDirectorPromise;
    } catch (error) {
      // Managing director info is optional, continue without it
      console.warn('[CompanyLookup] Managing director fetch failed:', error);
    }

    return {
      success: true,
      data: company
    };

  } catch (error) {
    console.error('[CompanyLookup] Lookup by org number failed:', error);
    
    return {
      success: false,
      error: {
        code: 'NETWORK_ERROR',
        message: 'Nettverksfeil. Sjekk internettforbindelsen din.',
        details: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

/**
 * Map API status to our simplified status
 */
function mapStatus(orgForm: string, konkurs: boolean, underAvvikling: boolean): CompanyInfo['status'] {
  if (konkurs || underAvvikling) {
    return 'dissolved';
  }
  
  // Most common active organization forms
  const activeOrgForms = ['AS', 'ASA', 'ENK', 'ANS', 'DA', 'NUF', 'BA', 'BRL', 'GFS', 'SPA', 'SF', 'IKS'];
  
  if (activeOrgForms.includes(orgForm)) {
    return 'active';
  }
  
  return 'inactive';
}

/**
 * Format organization number with spaces for display
 * @param orgNumber Raw organization number
 * @returns Formatted organization number (e.g., "***********")
 */
export function formatOrgNumber(orgNumber: string): string {
  const clean = orgNumber.replace(/\D/g, '');
  if (clean.length === 9) {
    return `${clean.slice(0, 3)} ${clean.slice(3, 6)} ${clean.slice(6)}`;
  }
  return orgNumber;
}

/**
 * Validate Norwegian organization number using MOD11 algorithm
 * @param orgNumber Organization number to validate
 * @returns True if valid, false otherwise
 */
export function validateOrgNumber(orgNumber: string): boolean {
  const clean = orgNumber.replace(/\D/g, '');

  if (clean.length !== 9) {
    return false;
  }

  // MOD11 validation for Norwegian organization numbers
  const weights = [3, 2, 7, 6, 5, 4, 3, 2];
  let sum = 0;

  for (let i = 0; i < 8; i++) {
    sum += parseInt(clean[i]) * weights[i];
  }

  const remainder = sum % 11;
  const checkDigit = remainder === 0 ? 0 : 11 - remainder;

  return checkDigit === parseInt(clean[8]);
}

/**
 * Format Norwegian date to DD.MM.YYYY format
 * @param dateString ISO date string (YYYY-MM-DD)
 * @returns Formatted date string or empty string if invalid
 */
export function formatNorwegianDate(dateString?: string): string {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';

    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${day}.${month}.${year}`;
  } catch {
    return '';
  }
}

/**
 * Format organization form for display
 * @param orgForm Organization form code (e.g., "AS", "ENK")
 * @param orgFormDescription Full description (e.g., "Aksjeselskap")
 * @returns Formatted organization form string
 */
export function formatOrganizationForm(orgForm?: string, orgFormDescription?: string): string {
  if (!orgForm && !orgFormDescription) return '';

  if (orgForm && orgFormDescription) {
    return `${orgFormDescription} (${orgForm})`;
  }

  return orgForm || orgFormDescription || '';
}

/**
 * Extract phone number from Norwegian registry format and convert to 8-digit format
 * @param registryPhone Phone number from Brønnøysundregisteret
 * @returns 8-digit phone number or empty string
 */
export function extractNorwegianPhone(registryPhone?: string): string {
  console.log('[extractNorwegianPhone] Input:', registryPhone);

  if (!registryPhone) {
    console.log('[extractNorwegianPhone] No input provided, returning empty string');
    return '';
  }

  // Remove all non-digits (including + symbol and spaces)
  const digits = registryPhone.replace(/\D/g, '');
  console.log('[extractNorwegianPhone] Digits extracted:', digits);

  // Handle different Norwegian phone number formats:
  // +4793209260 -> 93209260 (mobile with + prefix)
  // 4793209260 -> 93209260 (mobile with country code)
  // 93209260 -> 93209260 (mobile without country code)
  // +4722123456 -> 22123456 (landline with + prefix)
  // 004793209260 -> 93209260 (alternative international format)

  if (digits.startsWith('0047') && digits.length === 12) {
    const result = digits.substring(4);
    console.log('[extractNorwegianPhone] Converted from 0047 format:', result);
    return result;
  }

  if (digits.startsWith('47') && digits.length === 10) {
    const result = digits.substring(2);
    console.log('[extractNorwegianPhone] Converted from +47/47 format:', result);
    return result;
  }

  if (digits.length === 8) {
    console.log('[extractNorwegianPhone] Already 8-digit format:', digits);
    return digits;
  }

  console.log('[extractNorwegianPhone] Invalid format, returning empty string. Length:', digits.length, 'Digits:', digits);
  return '';
}

/**
 * Format industry code and description for display
 * @param code NACE industry code (e.g., "41201")
 * @param description Industry description
 * @returns Formatted industry string
 */
export function formatIndustryInfo(code?: string, description?: string): string {
  if (!code && !description) return '';

  if (code && description) {
    return `${description} (${code})`;
  }

  return code || description || '';
}
