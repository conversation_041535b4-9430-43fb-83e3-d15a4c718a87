import type { Id } from "../../convex/_generated/dataModel";

// Base message interface matching Convex schema
export interface Message {
  _id: Id<"messages">;
  _creationTime: number;
  logId: Id<"logEntries">;
  parentId?: Id<"messages">;
  senderId: string;
  senderRole: "customer" | "contractor";
  text?: string;
  file?: {
    url: string;
    name: string;
    size: number;
    type: string;
    thumbnailUrl?: string;
  };
  reactions?: {
    emoji: string;
    userIds: string[];
    count: number;
  }[];
  readBy?: Record<string, number>;
  deliveryStatus?: "sending" | "sent" | "delivered" | "failed";
  deliveredTo?: Record<string, number>;
  failureReason?: string;
  createdAt: number;
  updatedAt?: number;
  isEdited?: boolean;
  isDeleted?: boolean;
}

// Enhanced message with UI-specific properties
export interface MessageWithDisplayInfo extends Message {
  senderDisplayName: string;
  isOwnMessage: boolean;
  replies: MessageWithDisplayInfo[];
}

// Thread structure for UI
export interface MessageThread {
  messages: MessageWithDisplayInfo[];
  totalCount: number;
  rootCount: number;
  replyCount: number;
}

// Chat conversation overview
export interface ChatConversation {
  projectId: Id<"projects">;
  projectName: string;
  logId: Id<"logEntries">;
  logDescription: string;
  latestMessage: MessageWithDisplayInfo;
  unreadCount: number;
  lastActivity: number;
}

// Unread counts for dashboard
export interface UnreadCounts {
  totalUnread: number;
  conversationCounts: {
    projectId: Id<"projects">;
    projectName: string;
    logId: Id<"logEntries">;
    logDescription: string;
    unreadCount: number;
  }[];
}

// Message input form data
export interface MessageFormData {
  text?: string;
  file?: File | {
    url: string;
    name: string;
    size: number;
    type: string;
    thumbnailUrl?: string;
  };
  parentId?: Id<"messages">;
}

// Chat component props
export interface ChatContainerProps {
  logId: Id<"logEntries">;
  userId: string;
  userRole: "customer" | "contractor";
  className?: string;
}

export interface MessageListProps {
  messages: MessageWithDisplayInfo[];
  userId: string;
  onReply: (messageId: Id<"messages">) => void;
  onReaction: (messageId: Id<"messages">, emoji: string) => void;
  onEdit: (messageId: Id<"messages">) => void;
  onDelete: (messageId: Id<"messages">) => void;
  onRetry?: (optimisticId: string) => void;
  className?: string;
  userNames?: Record<string, string>; // Mapping of user IDs to display names for reactions
}

export interface MessageItemProps {
  message: MessageWithDisplayInfo;
  userId: string;
  level?: number; // For threading indentation
  onReply: (messageId: Id<"messages">) => void;
  onReaction: (messageId: Id<"messages">, emoji: string) => void;
  onEdit: (messageId: Id<"messages">) => void;
  onDelete: (messageId: Id<"messages">) => void;
  onRetry?: (optimisticId: string) => void;
  className?: string;
  userNames?: Record<string, string>; // Mapping of user IDs to display names for reactions
}

export interface MessageInputProps {
  logId: Id<"logEntries">;
  userId: string;
  userRole: "customer" | "contractor";
  parentId?: Id<"messages">;
  placeholder?: string;
  onSend: (data: MessageFormData) => void;
  onCancel?: () => void;
  onTypingStart?: () => void;
  onTypingStop?: () => void;
  className?: string;
}

// Emoji reaction props
export interface EmojiReactionProps {
  reactions: Message['reactions'];
  messageId: Id<"messages">;
  userId: string;
  onReaction: (messageId: Id<"messages">, emoji: string) => void;
  className?: string;
}

// File attachment props
export interface FileAttachmentProps {
  file: Message['file'];
  className?: string;
}

// Chat status and connection
export interface ChatStatus {
  isConnected: boolean;
  isTyping: boolean;
  lastSeen?: number;
}

// Typing indicator
export interface TypingIndicator {
  userId: string;
  userRole: "customer" | "contractor";
  displayName: string;
  timestamp: number;
}

// Optimistic message for immediate UI updates
export interface OptimisticMessage extends Omit<Message, '_id' | '_creationTime'> {
  _id: string; // Temporary ID
  _creationTime: number;
  isOptimistic: true;
  isSending?: boolean;
  sendError?: string;
}

// Error handling
export interface ChatError {
  type: 'network' | 'permission' | 'validation' | 'unknown';
  message: string;
  timestamp: number;
}
