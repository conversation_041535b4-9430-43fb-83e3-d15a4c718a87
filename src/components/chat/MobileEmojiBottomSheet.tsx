import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';

// Enhanced emoji palette for mobile bottom sheet with prioritization
const MOBILE_REACTIONS = [
  { emoji: '👍', label: 'Liker', priority: 1 },
  { emoji: '❤️', label: 'Elsker', priority: 2 },
  { emoji: '😂', label: 'Morsom', priority: 3 },
  { emoji: '😮', label: 'Overrasket', priority: 4 },
  { emoji: '😢', label: 'Trist', priority: 5 },
  { emoji: '😡', label: 'Sint', priority: 6 },
  { emoji: '🎉', label: 'Feirer', priority: 7 },
  { emoji: '🔥', label: 'Fantastisk', priority: 8 }
];

interface MobileEmojiBottomSheetProps {
  /** Whether the bottom sheet is open */
  isOpen: boolean;
  /** Callback when emoji is selected */
  onSelect: (emoji: string) => void;
  /** Callback when bottom sheet should close */
  onClose: () => void;
  /** Currently dragged/hovered emoji */
  selectedEmoji: string | null;
  /** Whether a reaction is being processed */
  isLoading: boolean;
}

/**
 * Mobile-optimized emoji bottom sheet for reaction selection
 * 
 * Features:
 * - Slides up from bottom on mobile devices (≤480px)
 * - Semi-transparent overlay background
 * - Horizontal scrollable emoji list
 * - Touch-optimized 44px minimum touch targets
 * - Swipe down to close gesture
 * - Haptic feedback on selection
 */
export const MobileEmojiBottomSheet: React.FC<MobileEmojiBottomSheetProps> = ({
  isOpen,
  onSelect,
  onClose,
  selectedEmoji,
  isLoading
}) => {
  const sheetRef = useRef<HTMLDivElement>(null);
  const startY = useRef<number>(0);
  const currentY = useRef<number>(0);

  // Handle swipe down to close
  const handleTouchStart = (e: React.TouchEvent) => {
    startY.current = e.touches[0].clientY;
    currentY.current = e.touches[0].clientY;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    currentY.current = e.touches[0].clientY;
    const deltaY = currentY.current - startY.current;
    
    // Only allow downward swipes
    if (deltaY > 0 && sheetRef.current) {
      sheetRef.current.style.transform = `translateY(${deltaY}px)`;
    }
  };

  const handleTouchEnd = () => {
    const deltaY = currentY.current - startY.current;
    
    if (sheetRef.current) {
      // Reset transform
      sheetRef.current.style.transform = '';
      
      // Close if swiped down more than 50px
      if (deltaY > 50) {
        onClose();
      }
    }
  };

  // Handle emoji selection with haptic feedback
  const handleEmojiSelect = (emoji: string) => {
    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
    
    onSelect(emoji);
  };

  // Prevent body scroll when sheet is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [isOpen]);

  // Close on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return createPortal(
    <div className="fixed inset-0 z-50 md:hidden">
      {/* Overlay */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50 transition-opacity duration-200"
        onClick={onClose}
        aria-hidden="true"
      />
      
      {/* Bottom Sheet */}
      <div
        ref={sheetRef}
        className="
          absolute bottom-0 left-0 right-0
          bg-white rounded-t-xl shadow-xl
          transform transition-transform duration-300 ease-out
          animate-in slide-in-from-bottom-full
        "
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        role="dialog"
        aria-label="Velg emoji reaksjon"
        aria-modal="true"
      >
        {/* Handle bar */}
        <div className="flex justify-center pt-3 pb-2">
          <div className="w-8 h-1 bg-jobblogg-border rounded-full" />
        </div>
        
        {/* Header */}
        <div className="px-4 pb-2">
          <h3 className="text-lg font-semibold text-jobblogg-text-strong">
            Velg reaksjon
          </h3>
        </div>
        
        {/* Emoji Grid */}
        <div className="px-4 pb-6">
          <div className="flex items-center gap-3 overflow-x-auto pb-2 scrollbar-hide">
            {MOBILE_REACTIONS.map(({ emoji, label, priority }) => (
              <button
                key={emoji}
                onClick={() => handleEmojiSelect(emoji)}
                disabled={isLoading}
                className={`
                  flex-shrink-0 min-h-[56px] min-w-[56px] p-3
                  bg-jobblogg-surface border border-jobblogg-border rounded-xl
                  hover:bg-jobblogg-surface-hover hover:border-jobblogg-primary
                  active:scale-95 transition-all duration-150
                  focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2
                  ${priority === 1 ? 'ring-1 ring-jobblogg-primary ring-opacity-30' : ''}
                  ${selectedEmoji === emoji ? 'scale-110 bg-jobblogg-primary border-jobblogg-primary text-white' : ''}
                  ${selectedEmoji && selectedEmoji !== emoji ? 'scale-90 opacity-70' : ''}
                `}
                aria-label={`Reager med ${emoji} (${label})`}
                title={label}
              >
                <span className="text-2xl">{emoji}</span>
              </button>
            ))}
          </div>

          {/* Instructions */}
          <p className="text-xs text-jobblogg-text-muted mt-4 text-center">
            Trykk på en emoji for å reagere, eller dra ned for å lukke
          </p>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default MobileEmojiBottomSheet;
