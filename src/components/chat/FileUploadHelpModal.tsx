import React from 'react';
import { Modal } from '../ui/Dialog/Modal';

export interface FileUploadHelpModalProps {
  isOpen: boolean;
  onClose: () => void;
  isMobile?: boolean;
}

// Modal-friendly help content component
const ModalFileUploadHelp: React.FC<{ isMobile: boolean }> = ({ isMobile }) => {
  return (
    <div className="space-y-4">
      {/* File upload section */}
      <div>
        <div className="font-medium text-jobblogg-text-strong mb-2">📎 Filopplasting</div>
        <div className="text-jobblogg-text-medium text-sm">
          <div className="flex flex-wrap gap-1 mb-2">
            <span className="bg-jobblogg-primary/10 text-jobblogg-primary px-2 py-0.5 rounded text-xs font-medium">JPG</span>
            <span className="bg-jobblogg-primary/10 text-jobblogg-primary px-2 py-0.5 rounded text-xs font-medium">PNG</span>
            <span className="bg-jobblogg-primary/10 text-jobblogg-primary px-2 py-0.5 rounded text-xs font-medium">WebP</span>
            <span className="bg-jobblogg-primary/10 text-jobblogg-primary px-2 py-0.5 rounded text-xs font-medium">PDF</span>
          </div>
          <div className="space-y-1">
            <div>📏 Maks størrelse: 10MB</div>
            <div>🖼️ Bilder vises automatisk i chatten</div>
          </div>
        </div>
      </div>

      {/* Divider */}
      <div className="border-t border-jobblogg-border"></div>

      {/* Camera section */}
      <div>
        <div className="font-medium text-jobblogg-text-strong mb-2">📷 Kamerafunksjon</div>
        <div className="text-jobblogg-text-medium text-sm space-y-1">
          <div>📸 Ta bilder direkte med kameraet</div>
          <div>🔄 Automatisk optimalisering for web</div>
          <div>💾 Lagres umiddelbart i chatten</div>
        </div>
      </div>

      {/* Platform-specific instructions */}
      <div className="border-t border-jobblogg-border pt-4">
        <div className="font-medium text-jobblogg-text-strong mb-2">
          {isMobile ? '📱 Mobilinstruksjoner' : '🖥️ Desktop-instruksjoner'}
        </div>
        <div className="text-jobblogg-text-medium text-sm">
          <ul className="space-y-1">
            {isMobile ? (
              <>
                <li>• Trykk på 📎-ikonet for å velge filer fra enheten</li>
                <li>• Trykk på 📷-ikonet for å ta nye bilder</li>
                <li>• Hold enheten stabilt for beste bildekvalitet</li>
                <li>• Sørg for god belysning når du tar bilder</li>
              </>
            ) : (
              <>
                <li>• Klikk på 📎-ikonet for å velge filer</li>
                <li>• Dra og slipp filer direkte i meldingsfeltet</li>
                <li>• Bruk kamera-ikonet hvis du har webkamera</li>
                <li>• Støttede filformater vises automatisk som forhåndsvisning</li>
              </>
            )}
          </ul>
        </div>
      </div>
    </div>
  );
};

export const FileUploadHelpModal: React.FC<FileUploadHelpModalProps> = ({
  isOpen,
  onClose,
  isMobile = false
}) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Hjelp for filopplasting og kamera"
      size="md"
      className="max-w-md"
      closeOnOverlayClick={true}
      showCloseButton={true}
    >
      <ModalFileUploadHelp isMobile={isMobile} />
    </Modal>
  );
};

export default FileUploadHelpModal;
