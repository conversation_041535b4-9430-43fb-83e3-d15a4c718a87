import React from 'react';

export interface FileUploadHelpProps {
  type: 'file' | 'camera';
  isMobile?: boolean;
}

export const FileUploadHelp: React.FC<FileUploadHelpProps> = ({ type, isMobile = false }) => {
  const fileContent = (
    <div className="space-y-2">
      <div className="font-medium text-white">📎 Støttede filtyper:</div>
      <div className="text-white/90 text-xs">
        <div className="flex flex-wrap gap-1 mb-2">
          <span className="bg-white/20 px-2 py-1 rounded text-xs">JPG</span>
          <span className="bg-white/20 px-2 py-1 rounded text-xs">PNG</span>
          <span className="bg-white/20 px-2 py-1 rounded text-xs">WebP</span>
          <span className="bg-white/20 px-2 py-1 rounded text-xs">PDF</span>
        </div>
        <div className="space-y-1">
          <div>📏 Maks størrelse: 10MB</div>
          <div>🖼️ Bilder vises automatisk i chatten</div>
          {isMobile && (
            <div className="pt-2 border-t border-white/20">
              <div className="text-xs text-white/80">
                💡 Tips: Bruk kameraknappen for å ta nye bilder direkte
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const cameraContent = (
    <div className="space-y-2">
      <div className="font-medium text-white">📷 Kamerafunksjon:</div>
      <div className="text-white/90 text-xs space-y-1">
        <div>📸 Ta bilder direkte med kameraet</div>
        <div>🔄 Automatisk optimalisering for web</div>
        <div>💾 Lagres umiddelbart i chatten</div>
        {isMobile && (
          <div className="pt-2 border-t border-white/20">
            <div className="text-xs text-white/80">
              💡 Tips: Sørg for god belysning for beste kvalitet
            </div>
          </div>
        )}
      </div>
    </div>
  );

  return type === 'file' ? fileContent : cameraContent;
};

export const FileUploadHelpIcon: React.FC<{ className?: string }> = ({ className = '' }) => (
  <svg 
    className={`text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors ${className}`} 
    fill="none" 
    stroke="currentColor" 
    viewBox="0 0 24 24"
  >
    <path 
      strokeLinecap="round" 
      strokeLinejoin="round" 
      strokeWidth={2} 
      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
    />
  </svg>
);
