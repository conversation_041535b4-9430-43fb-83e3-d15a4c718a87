import React, { useRef, useEffect, useState } from 'react';
import { MessageListProps } from '../../types/chat';
import { MessageItem } from './MessageItem';
import type { Id } from '../../../convex/_generated/dataModel';

export const MessageList: React.FC<MessageListProps> = ({
  messages,
  userId,
  onReply,
  onReaction,
  onEdit,
  onDelete,
  onRetry,
  className = '',
  userNames = {}
}) => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const prevMessageCountRef = useRef(messages.length);
  const [collapsedThreads, setCollapsedThreads] = useState<Set<string>>(new Set());

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollRef.current && messages.length > prevMessageCountRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
    prevMessageCountRef.current = messages.length;
  }, [messages.length]);

  // Initial scroll to bottom
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, []);

  // Toggle thread collapse/expand
  const toggleThreadCollapse = (messageId: Id<"messages">) => {
    setCollapsedThreads(prev => {
      const newSet = new Set(prev);
      if (newSet.has(messageId)) {
        newSet.delete(messageId);
      } else {
        newSet.add(messageId);
      }
      return newSet;
    });
  };

  if (messages.length === 0) {
    return (
      <div className={`jobblogg-message-list ${className}`}>
        <div className="flex flex-col items-center justify-center h-full text-center py-12">
          <div className="w-16 h-16 bg-jobblogg-card rounded-full flex items-center justify-center mb-4">
            <svg
              className="w-8 h-8 text-jobblogg-text-muted"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-2">
            Ingen meldinger ennå
          </h3>
          <p className="text-jobblogg-text-muted max-w-sm">
            Start en samtale ved å skrive den første meldingen nedenfor.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={scrollRef}
      className={`jobblogg-message-list overflow-y-auto h-full px-4 py-4 space-y-4 ${className}`}
    >
      {messages.map((message) => {
        const isCollapsed = collapsedThreads.has(message._id);
        const hasReplies = message.replies.length > 0;

        return (
          <div key={message._id} className="message-thread">
            {/* Root message */}
            <MessageItem
              message={message}
              userId={userId}
              level={0}
              onReply={onReply}
              onReaction={onReaction}
              onEdit={onEdit}
              onDelete={onDelete}
              onRetry={onRetry}
              userNames={userNames}
            />

            {/* Thread controls */}
            {hasReplies && (
              <div className="ml-8 mt-2 mb-3">
                <button
                  onClick={() => toggleThreadCollapse(message._id)}
                  className="flex items-center space-x-2 text-xs text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors"
                >
                  <svg
                    className={`w-3 h-3 transition-transform ${isCollapsed ? 'rotate-0' : 'rotate-90'}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  <span>
                    {isCollapsed
                      ? `Vis ${message.replies.length} svar`
                      : `Skjul ${message.replies.length} svar`
                    }
                  </span>
                </button>
              </div>
            )}

            {/* Replies */}
            {hasReplies && !isCollapsed && (
              <div className="ml-8 space-y-3 border-l-2 border-jobblogg-border pl-4">
                {message.replies.map((reply) => (
                  <MessageItem
                    key={reply._id}
                    message={reply}
                    userId={userId}
                    level={1}
                    onReply={onReply}
                    onReaction={onReaction}
                    onEdit={onEdit}
                    onDelete={onDelete}
                    onRetry={onRetry}
                    userNames={userNames}
                  />
                ))}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};
