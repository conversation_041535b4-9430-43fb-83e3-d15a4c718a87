import React from 'react';

export interface CombinedFileUploadHelpProps {
  isMobile?: boolean;
}

export const CombinedFileUploadHelp: React.FC<CombinedFileUploadHelpProps> = ({ isMobile = false }) => {
  return (
    <div className="space-y-3">
      {/* File upload section */}
      <div>
        <div className="font-medium text-white mb-1">📎 Filopplasting</div>
        <div className="text-white/90 text-xs">
          <div className="flex flex-wrap gap-1 mb-1">
            <span className="bg-white/20 px-2 py-0.5 rounded text-xs">JPG</span>
            <span className="bg-white/20 px-2 py-0.5 rounded text-xs">PNG</span>
            <span className="bg-white/20 px-2 py-0.5 rounded text-xs">WebP</span>
            <span className="bg-white/20 px-2 py-0.5 rounded text-xs">PDF</span>
          </div>
          <div className="space-y-0.5">
            <div>📏 Maks størrelse: 10MB</div>
            <div>🖼️ Bilder vises automatisk i chatten</div>
          </div>
        </div>
      </div>

      {/* Divider */}
      <div className="border-t border-white/20"></div>

      {/* Camera section */}
      <div>
        <div className="font-medium text-white mb-1">📷 Kamerafunksjon</div>
        <div className="text-white/90 text-xs space-y-0.5">
          <div>📸 Ta bilder direkte med kameraet</div>
          <div>🔄 Automatisk optimalisering for web</div>
          <div>💾 Lagres umiddelbart i chatten</div>
        </div>
      </div>

      {/* Mobile tips */}
      {isMobile && (
        <>
          <div className="border-t border-white/20"></div>
          <div className="text-xs text-white/80">
            <div className="font-medium text-white mb-1">💡 Tips</div>
            <div className="space-y-0.5">
              <div>• Trykk på binders-ikonet for å laste opp filer</div>
              <div>• Trykk på kamera-ikonet for å ta bilder</div>
              <div>• Sørg for god belysning for beste kvalitet</div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export const FileUploadHelpIcon: React.FC<{ className?: string }> = ({ className = '' }) => (
  <svg 
    className={`text-jobblogg-text-muted hover:text-jobblogg-primary transition-colors ${className}`} 
    fill="none" 
    stroke="currentColor" 
    viewBox="0 0 24 24"
  >
    <path 
      strokeLinecap="round" 
      strokeLinejoin="round" 
      strokeWidth={2} 
      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
    />
  </svg>
);
