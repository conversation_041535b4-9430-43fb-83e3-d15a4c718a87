import React, { useState, useEffect, useRef, useCallback } from 'react';
import { getAddressSuggestions, debounce } from '../../../utils/addressApi';
import type { AddressSuggestion } from '../../../utils/addressApi';

export interface AddressAutocompleteProps {
  label: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  onAddressSelect?: (suggestion: AddressSuggestion) => void;
  error?: string;
  helperText?: string;
  required?: boolean;
  fullWidth?: boolean;
  className?: string;
  disabled?: boolean;
}

export const AddressAutocomplete: React.FC<AddressAutocompleteProps> = ({
  label,
  placeholder = '',
  value,
  onChange,
  onAddressSelect,
  error,
  helperText,
  required = false,
  fullWidth = false,
  className = '',
  disabled = false
}) => {
  const [suggestions, setSuggestions] = useState<AddressSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (query: string) => {
      if (query.length < 3) {
        setSuggestions([]);
        setIsLoading(false);
        return;
      }

      try {
        const results = await getAddressSuggestions(query);
        setSuggestions(results);
        setShowSuggestions(true);
      } catch (error) {
        console.error('Address search failed:', error);
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    }, 300),
    []
  );

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return;

    const newValue = e.target.value;
    onChange(newValue);
    setSelectedIndex(-1);

    if (newValue.length >= 3) {
      setIsLoading(true);
      debouncedSearch(newValue);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
      setIsLoading(false);
    }
  };

  // Handle suggestion selection
  const handleSuggestionSelect = (suggestion: AddressSuggestion) => {
    onChange(suggestion.address);
    setSuggestions([]);
    setShowSuggestions(false);
    setSelectedIndex(-1);
    
    if (onAddressSelect) {
      onAddressSelect(suggestion);
    }
    
    inputRef.current?.focus();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (disabled || !showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleSuggestionSelect(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  // Handle click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        inputRef.current &&
        !inputRef.current.contains(event.target as Node) &&
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const baseClasses = `
    relative block w-full px-3 py-2 text-base
    border rounded-lg
    placeholder-jobblogg-text-muted
    transition-colors duration-200
    ${disabled
      ? 'bg-jobblogg-card-bg border-jobblogg-border text-jobblogg-text-medium cursor-not-allowed opacity-75'
      : 'bg-white focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/20 focus:border-jobblogg-primary'
    }
    ${error && !disabled
      ? 'border-jobblogg-error text-jobblogg-error focus:border-jobblogg-error focus:ring-jobblogg-error/20'
      : !disabled ? 'border-jobblogg-border text-jobblogg-text-strong hover:border-jobblogg-border-hover' : ''
    }
    ${fullWidth ? 'w-full' : ''}
  `.trim();

  return (
    <div className={`relative ${fullWidth ? 'w-full' : ''} ${className}`}>
      {/* Label */}
      <label className="block text-sm font-medium text-jobblogg-text-strong mb-2">
        {label}
        {required && <span className="text-jobblogg-error ml-1">*</span>}
      </label>

      {/* Input with loading indicator */}
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (!disabled && suggestions.length > 0) {
              setShowSuggestions(true);
            }
          }}
          placeholder={placeholder}
          className={baseClasses}
          autoComplete="off"
          disabled={disabled}
        />
        
        {/* Loading indicator */}
        {isLoading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="w-4 h-4 border-2 border-jobblogg-primary/20 border-t-jobblogg-primary rounded-full animate-spin"></div>
          </div>
        )}
      </div>

      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 w-full mt-1 bg-white border border-jobblogg-border rounded-lg shadow-lg max-h-60 overflow-y-auto"
        >
          {suggestions.map((suggestion, index) => (
            <button
              key={`${suggestion.source}-${index}`}
              type="button"
              onClick={() => handleSuggestionSelect(suggestion)}
              className={`
                w-full px-4 py-3 text-left hover:bg-jobblogg-background-soft
                border-b border-jobblogg-border last:border-b-0
                focus:outline-none focus:bg-jobblogg-primary-soft
                transition-colors duration-150
                ${selectedIndex === index ? 'bg-jobblogg-primary-soft' : ''}
              `}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-jobblogg-text-strong truncate">
                    {suggestion.address}
                  </div>
                  <div className="text-xs text-jobblogg-text-muted mt-1">
                    {suggestion.postalCode} {suggestion.city}
                  </div>
                </div>
                <div className="ml-2 flex-shrink-0">
                  <span className={`
                    inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                    ${suggestion.source === 'kartverket' 
                      ? 'bg-jobblogg-success-soft text-jobblogg-success' 
                      : 'bg-jobblogg-primary-soft text-jobblogg-primary'
                    }
                  `}>
                    {suggestion.source === 'kartverket' ? '🇳🇴' : '🌍'}
                  </span>
                </div>
              </div>
            </button>
          ))}
        </div>
      )}

      {/* Helper text or error */}
      {(helperText || error) && (
        <p className={`mt-2 text-sm ${error ? 'text-jobblogg-error' : 'text-jobblogg-text-muted'}`}>
          {error || helperText}
        </p>
      )}
    </div>
  );
};
