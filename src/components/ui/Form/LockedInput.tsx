import React from 'react';

interface LockedInputProps {
  label: string;
  value: string;
  helperText?: string;
  fullWidth?: boolean;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

/**
 * LockedInput component for displaying read-only form fields
 *
 * Features:
 * - WCAG AA compliant read-only styling with proper contrast ratios
 * - Lock icon visual indicator
 * - Consistent styling with JobbLogg design system
 * - Screen reader friendly with proper ARIA attributes
 *
 * @example
 * ```tsx
 * <LockedInput
 *   label="Organisasjonsnummer"
 *   value="123456789"
 *   helperText="Automatisk utfylt"
 * />
 * ```
 */
export const LockedInput: React.FC<LockedInputProps> = ({
  label,
  value,
  helperText,
  fullWidth = false,
  size = 'medium',
  className = ''
}) => {

  // Size classes
  const sizeClasses = {
    small: 'px-3 py-2 text-sm',
    medium: 'px-4 py-3 text-base',
    large: 'px-4 py-4 text-lg'
  };

  return (
    <div className={`${fullWidth ? 'w-full' : ''} ${className}`}>
      {/* Label */}
      <label className="block text-sm font-medium text-jobblogg-text-strong mb-2">
        {label}
        <span className="ml-2 inline-flex items-center">
          <svg
            className="w-4 h-4 text-jobblogg-text-muted"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
            />
          </svg>
        </span>
      </label>

      {/* Input Field */}
      <input
        type="text"
        value={value}
        readOnly
        aria-readonly="true"
        aria-describedby={helperText ? `${label}-helper` : undefined}
        className={`
          ${sizeClasses[size]}
          ${fullWidth ? 'w-full' : ''}
          bg-jobblogg-card-bg
          border border-jobblogg-border
          rounded-lg
          text-jobblogg-text-medium
          cursor-not-allowed
          opacity-75
          focus:outline-none
          focus:ring-2
          focus:ring-jobblogg-primary/30
          focus:border-jobblogg-primary
          transition-colors
          duration-200
        `}
      />

      {/* Helper Text */}
      {helperText && (
        <p id={`${label}-helper`} className="mt-2 text-sm text-jobblogg-text-muted">
          {helperText}
        </p>
      )}
    </div>
  );
};
