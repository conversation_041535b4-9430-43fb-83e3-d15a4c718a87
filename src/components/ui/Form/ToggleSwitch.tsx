import React from 'react';

interface ToggleSwitchProps {
  label: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  helperText?: string;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

/**
 * ToggleSwitch component following JobbLogg design system
 * 
 * Features:
 * - WCAG AA accessibility compliance with proper focus states
 * - Keyboard navigation support (Space/Enter to toggle)
 * - Screen reader friendly with proper ARIA attributes
 * - Smooth animations and visual feedback
 * - Consistent styling with JobbLogg design tokens
 * 
 * @example
 * ```tsx
 * <ToggleSwitch
 *   label="Bruk annen prosjektadresse"
 *   checked={useCustomAddress}
 *   onChange={setUseCustomAddress}
 *   helperText="Aktiver for å bruke en annen adresse enn bedriftens registrerte adresse"
 * />
 * ```
 */
export const ToggleSwitch: React.FC<ToggleSwitchProps> = ({
  label,
  checked,
  onChange,
  disabled = false,
  helperText,
  size = 'medium',
  className = ''
}) => {
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === ' ' || event.key === 'Enter') {
      event.preventDefault();
      if (!disabled) {
        onChange(!checked);
      }
    }
  };

  // Size classes
  const sizeClasses = {
    small: {
      switch: 'w-8 h-4',
      thumb: 'w-3 h-3',
      translate: 'translate-x-4'
    },
    medium: {
      switch: 'w-10 h-5',
      thumb: 'w-4 h-4',
      translate: 'translate-x-5'
    },
    large: {
      switch: 'w-12 h-6',
      thumb: 'w-5 h-5',
      translate: 'translate-x-6'
    }
  };

  const currentSize = sizeClasses[size];

  return (
    <div className={`${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <label 
            htmlFor={`toggle-${label}`}
            className={`block text-sm font-medium ${
              disabled ? 'text-jobblogg-text-muted' : 'text-jobblogg-text-strong'
            } cursor-pointer`}
          >
            {label}
          </label>
          {helperText && (
            <p className="mt-1 text-xs text-jobblogg-text-muted">
              {helperText}
            </p>
          )}
        </div>

        {/* Toggle Switch */}
        <div className="ml-4">
          <button
            id={`toggle-${label}`}
            type="button"
            role="switch"
            aria-checked={checked}
            aria-describedby={helperText ? `${label}-helper` : undefined}
            disabled={disabled}
            onClick={() => !disabled && onChange(!checked)}
            onKeyDown={handleKeyDown}
            className={`
              ${currentSize.switch}
              relative inline-flex items-center rounded-full
              transition-colors duration-200 ease-in-out
              focus:outline-none focus:ring-2 focus:ring-jobblogg-primary/30 focus:ring-offset-2
              ${disabled 
                ? 'bg-jobblogg-border cursor-not-allowed opacity-50' 
                : checked 
                  ? 'bg-jobblogg-primary hover:bg-jobblogg-primary-hover' 
                  : 'bg-jobblogg-border hover:bg-jobblogg-text-muted/20'
              }
            `}
          >
            <span className="sr-only">
              {checked ? 'Deaktiver' : 'Aktiver'} {label}
            </span>
            
            {/* Thumb */}
            <span
              className={`
                ${currentSize.thumb}
                inline-block rounded-full bg-white shadow-sm
                transform transition-transform duration-200 ease-in-out
                ${checked ? currentSize.translate : 'translate-x-0.5'}
              `}
            />
          </button>
        </div>
      </div>
    </div>
  );
};
