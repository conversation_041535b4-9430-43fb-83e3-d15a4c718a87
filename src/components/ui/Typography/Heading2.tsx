import React from 'react';

interface Heading2Props {
  /** Heading content */
  children: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** HTML element to render */
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

/**
 * Heading 2 component for section titles and major content divisions
 * Uses text-heading-2 (32px) with semibold weight for clear hierarchy
 * 
 * @example
 * ```tsx
 * <Heading2>Prosjektoversikt</Heading2>
 * <Heading2 as="h3">Siste aktivitet</Heading2>
 * <Heading2 className="mb-6">Seksjonstittel</Heading2>
 * ```
 */
export const Heading2: React.FC<Heading2Props> = ({
  children,
  className = '',
  as: Component = 'h2',
}) => {
  return (
    <Component
      className={`
        text-heading-2 text-jobblogg-text-strong font-semibold leading-tight break-words
        ${className}
      `.trim().replace(/\s+/g, ' ')}
    >
      {children}
    </Component>
  );
};

export default Heading2;
