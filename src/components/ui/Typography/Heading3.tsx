import React from 'react';

interface Heading3Props {
  /** Heading content */
  children: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** HTML element to render */
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

/**
 * Heading 3 component for subsection titles and card headers
 * Uses text-heading-3 (24px) with semibold weight for content organization
 * 
 * @example
 * ```tsx
 * <Heading3>Prosjektdetaljer</Heading3>
 * <Heading3 as="h4">Logginnlegg</Heading3>
 * <Heading3 className="mb-4">Kortoverskrift</Heading3>
 * ```
 */
export const Heading3: React.FC<Heading3Props> = ({
  children,
  className = '',
  as: Component = 'h3',
}) => {
  return (
    <Component
      className={`
        text-heading-3 text-jobblogg-text-strong font-semibold leading-snug break-words
        ${className}
      `.trim().replace(/\s+/g, ' ')}
    >
      {children}
    </Component>
  );
};

export default Heading3;
