import React from 'react';
import { Link } from 'react-router-dom';

interface PageLayoutProps {
  /** Page content */
  children: React.ReactNode;
  /** Page title */
  title?: string;
  /** Show back button */
  showBackButton?: boolean;
  /** Back button URL */
  backUrl?: string;
  /** Additional CSS classes */
  className?: string;
  /** Header actions (e.g., buttons, user menu) */
  headerActions?: React.ReactNode;
  /** Container width variant */
  containerWidth?: 'narrow' | 'medium' | 'wide' | 'full';
  /** Enable section spacing */
  sectionSpacing?: boolean;
  /** Custom header content below title */
  headerContent?: React.ReactNode;
  /** Disable default animations */
  noAnimation?: boolean;
}

/**
 * Page layout component providing consistent structure across the application
 * Includes optional header with title, back button, and actions
 *
 * @example
 * ```tsx
 * <PageLayout title="Opprett nytt prosjekt" showBackButton backUrl="/">
 *   <CreateProjectForm />
 * </PageLayout>
 *
 * <PageLayout
 *   title="Dashboard"
 *   headerActions={<UserButton />}
 *   containerWidth="full"
 *   sectionSpacing
 * >
 *   <ProjectGrid />
 * </PageLayout>
 *
 * <PageLayout
 *   title="Prosjektdetaljer"
 *   containerWidth="medium"
 *   headerContent={<ProjectStats />}
 * >
 *   <ProjectContent />
 * </PageLayout>
 * ```
 */
export const PageLayout: React.FC<PageLayoutProps> = ({
  children,
  title,
  showBackButton = false,
  backUrl = '/',
  className = '',
  headerActions,
  containerWidth = 'full',
  sectionSpacing = false,
  headerContent,
  noAnimation = false,
}) => {
  // Get container class based on width variant
  const getContainerClass = () => {
    switch (containerWidth) {
      case 'narrow':
        return 'container-narrow';
      case 'medium':
        return 'container-medium';
      case 'wide':
        return 'container-wide';
      default:
        return 'container-full';
    }
  };

  const animationClass = noAnimation ? '' : 'animate-fade-in';
  const spacingClass = sectionSpacing ? 'section-spacing' : 'container-section';

  return (
    <div className={`min-h-screen bg-white ${animationClass} ${className}`.trim()}>
      <div className={`${getContainerClass()} ${spacingClass}`}>
        {/* Header Section */}
        {(title || showBackButton || headerActions || headerContent) && (
          <header className="page-header">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-6 mb-6 sm:mb-8 w-full min-w-0">
              <div className="flex items-center gap-4 min-w-0 flex-shrink">
                {/* Back Button */}
                {showBackButton && (
                  <Link
                    to={backUrl}
                    className="inline-flex items-center justify-center w-10 h-10 rounded-xl bg-jobblogg-neutral hover:bg-jobblogg-primary-soft text-jobblogg-text-medium hover:text-jobblogg-primary transition-all duration-200 focus-ring hover-lift"
                    aria-label="Tilbake"
                  >
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 19l-7-7 7-7"
                      />
                    </svg>
                  </Link>
                )}

                {/* Page Title */}
                {title && (
                  <div className={`min-w-0 flex-shrink ${noAnimation ? '' : 'animate-slide-up'}`}>
                    <h1 className="text-heading-1 text-jobblogg-text-strong break-words leading-tight">
                      {title}
                    </h1>
                  </div>
                )}
              </div>

              {/* Header Actions */}
              {headerActions && (
                <div className="flex items-start sm:items-center w-full sm:w-auto min-w-0">
                  {headerActions}
                </div>
              )}
            </div>

            {/* Additional Header Content */}
            {headerContent && (
              <div className="space-component">
                {headerContent}
              </div>
            )}
          </header>
        )}

        {/* Main Content */}
        <main
          className={`page-content ${noAnimation ? '' : 'animate-slide-up'}`}
          style={noAnimation ? {} : { animationDelay: '0.1s' }}
        >
          {children}
        </main>
      </div>
    </div>
  );
};

export default PageLayout;
