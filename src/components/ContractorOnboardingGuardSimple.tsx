import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useUser, useAuth } from '@clerk/clerk-react';

interface ContractorOnboardingGuardSimpleProps {
  children: React.ReactNode;
}

/**
 * Simplified Contractor Onboarding Guard
 * 
 * This version uses localStorage to track onboarding completion
 * and doesn't rely on Convex authentication until after onboarding.
 * 
 * Flow:
 * 1. Check localStorage for completion status
 * 2. If not completed, redirect to onboarding
 * 3. After onboarding completion, mark as completed in localStorage
 * 4. Allow access to main app
 */
export const ContractorOnboardingGuardSimple: React.FC<ContractorOnboardingGuardSimpleProps> = ({ children }) => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isSignedIn, isLoaded: isAuthLoaded } = useAuth();
  const location = useLocation();
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(true);

  // Check onboarding completion from localStorage
  useEffect(() => {
    if (isClerkLoaded && isAuthLoaded) {
      if (!isSignedIn || !user) {
        setIsCheckingOnboarding(false);
      } else {
        // Check localStorage for onboarding completion
        const storageKey = `jobblogg-contractor-completed-${user.id}`;
        const isCompleted = localStorage.getItem(storageKey) === 'true';
        
        console.log(`Checking onboarding for user ${user.id}: ${isCompleted ? 'completed' : 'not completed'}`);
        setIsCheckingOnboarding(false);
      }
    }
  }, [isClerkLoaded, isAuthLoaded, isSignedIn, user]);

  // Show loading state while checking authentication
  if (!isClerkLoaded || !isAuthLoaded || isCheckingOnboarding) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 border-4 border-jobblogg-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-jobblogg-text-strong">
              Laster JobbLogg...
            </h2>
            <p className="text-jobblogg-text-muted">
              {!isClerkLoaded ? 'Starter autentisering...' : 
               !isAuthLoaded ? 'Synkroniserer med server...' : 
               'Sjekker brukerinformasjon...'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // If user is not authenticated, let Clerk handle the redirect
  if (!isSignedIn || !user) {
    return <Navigate to="/sign-in" replace />;
  }

  // Check if contractor onboarding is completed (from localStorage)
  const storageKey = `jobblogg-contractor-completed-${user.id}`;
  const isOnboardingCompleted = localStorage.getItem(storageKey) === 'true';

  // If contractor onboarding is not completed, redirect to onboarding
  if (!isOnboardingCompleted) {
    // Preserve the intended destination for redirect after onboarding
    const redirectTo = location.pathname !== '/contractor-onboarding' ? location.pathname : '/';
    const onboardingUrl = `/contractor-onboarding${redirectTo !== '/' ? `?redirect=${encodeURIComponent(redirectTo)}` : ''}`;
    
    return <Navigate to={onboardingUrl} replace />;
  }

  // Contractor onboarding is complete, render the protected content
  return <>{children}</>;
};

/**
 * Helper function to mark contractor onboarding as completed
 * Call this after successful onboarding completion
 */
export const markContractorOnboardingCompleted = (userId: string) => {
  const storageKey = `jobblogg-contractor-completed-${userId}`;
  localStorage.setItem(storageKey, 'true');
  console.log(`Marked contractor onboarding as completed for user ${userId}`);
};

/**
 * Helper function to reset contractor onboarding status
 * Useful for testing or if user needs to redo onboarding
 */
export const resetContractorOnboardingStatus = (userId: string) => {
  const storageKey = `jobblogg-contractor-completed-${userId}`;
  localStorage.removeItem(storageKey);
  console.log(`Reset contractor onboarding status for user ${userId}`);
};

/**
 * Comprehensive cleanup function for contractor onboarding
 * Removes all localStorage keys related to contractor onboarding
 */
export const clearAllContractorOnboardingData = (userId: string) => {
  const keysToRemove = [
    'jobblogg-contractor-onboarding', // Main onboarding data
    `jobblogg-contractor-completed-${userId}`, // Completion status
    'jobblogg-contractor-onboarding-temp', // Temporary data
    'jobblogg-contractor-form-backup', // Form backup
    'jobblogg-contractor-step-data', // Step-specific data
    'jobblogg-contractor-brreg-cache', // Brønnøysundregisteret cache
  ];

  let removedCount = 0;
  keysToRemove.forEach(key => {
    if (localStorage.getItem(key) !== null) {
      localStorage.removeItem(key);
      removedCount++;
      console.log(`[ContractorOnboarding] Removed localStorage key: ${key}`);
    }
  });

  // Also remove any keys that start with contractor onboarding patterns
  const allKeys = Object.keys(localStorage);
  const contractorKeys = allKeys.filter(key =>
    key.includes('contractor') &&
    (key.includes('onboarding') || key.includes('jobblogg'))
  );

  contractorKeys.forEach(key => {
    if (!keysToRemove.includes(key)) {
      localStorage.removeItem(key);
      removedCount++;
      console.log(`[ContractorOnboarding] Removed additional key: ${key}`);
    }
  });

  console.log(`[ContractorOnboarding] Cleared ${removedCount} localStorage keys for user ${userId}`);
  return removedCount;
};

/**
 * Hook to get contractor onboarding status (simplified version)
 * Uses localStorage instead of Convex queries
 */
export const useContractorOnboardingStatusSimple = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isSignedIn, isLoaded: isAuthLoaded } = useAuth();
  
  const isReady = isClerkLoaded && isAuthLoaded && isSignedIn && user?.id;
  
  if (!isReady) {
    return {
      isLoading: true,
      exists: false,
      contractorCompleted: false,
      contractorCompanyId: null,
    };
  }

  const storageKey = `jobblogg-contractor-completed-${user.id}`;
  const isCompleted = localStorage.getItem(storageKey) === 'true';

  return {
    isLoading: false,
    exists: true, // Assume user exists if they're authenticated
    contractorCompleted: isCompleted,
    contractorCompanyId: null, // Will be populated after Convex auth is working
  };
};

/**
 * Hook to check if we should show onboarding
 * Useful for conditional rendering in components
 */
export const useShouldShowOnboarding = () => {
  const { user } = useUser();
  
  if (!user?.id) {
    return false;
  }

  const storageKey = `jobblogg-contractor-completed-${user.id}`;
  const isCompleted = localStorage.getItem(storageKey) === 'true';
  
  return !isCompleted;
};
