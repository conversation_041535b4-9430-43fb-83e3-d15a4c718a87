import React, { useState, useEffect } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../convex/_generated/api';
import {
  Modal,
  PrimaryButton,
  SecondaryButton,
  TextInput,
  PhoneInput,
  TextArea,
  TextStrong,
  TextMedium,
  TextMuted
} from './ui';
import { useCompanyLookup } from '../hooks/useCompanyLookup';
import type { CompanyInfo } from '../services/companyLookup';
import { companyProfileTexts } from '../localization/companyProfile';

interface CompanyProfileModalProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Function to close the modal */
  onClose: () => void;
  /** Optional callback when company is successfully updated */
  onSuccess?: () => void;
}

interface CompanyFormData {
  companyName: string;
  orgNumber: string;
  contactPerson: string;
  phone: string;
  email: string;
  streetAddress: string;
  postalCode: string;
  city: string;
  entrance: string;
  notes: string;
  useCustomAddress: boolean;
}

/**
 * Company Profile Modal Component
 * 
 * Allows contractors to view and edit their company information
 * with Brønnøysundregisteret integration and Norwegian localization.
 * 
 * Features:
 * - Full company information editing
 * - Brønnøysundregisteret data refresh
 * - Address override functionality
 * - Norwegian form validation
 * - JobbLogg design system compliance
 * 
 * @example
 * ```tsx
 * <CompanyProfileModal
 *   isOpen={showModal}
 *   onClose={() => setShowModal(false)}
 *   onSuccess={() => console.log('Company updated!')}
 * />
 * ```
 */
export const CompanyProfileModal: React.FC<CompanyProfileModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const { user } = useUser();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isRefreshingBrreg, setIsRefreshingBrreg] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [successMessage, setSuccessMessage] = useState<string>('');

  // Get current company data
  const contractorCompany = useQuery(
    api.contractorCompany.getContractorCompanyWithDetails,
    user?.id ? { clerkUserId: user.id } : "skip"
  );

  // Company lookup hook for Brønnøysundregisteret integration
  const {
    searchByOrgNumber,
    selectedCompany,
    isLoading: isLookupLoading,
    error: lookupError,
    clearResults
  } = useCompanyLookup();

  // Form state
  const [formData, setFormData] = useState<CompanyFormData>({
    companyName: '',
    orgNumber: '',
    contactPerson: '',
    phone: '',
    email: '',
    streetAddress: '',
    postalCode: '',
    city: '',
    entrance: '',
    notes: '',
    useCustomAddress: false
  });

  // Mutations
  const updateCompany = useMutation(api.contractorCompany.updateContractorCompany);
  const refreshBrreg = useMutation(api.contractorCompany.refreshBrregData);

  // Load company data when modal opens
  useEffect(() => {
    if (isOpen && contractorCompany) {
      setFormData({
        companyName: contractorCompany.name || '',
        orgNumber: contractorCompany.orgNumber || '',
        contactPerson: contractorCompany.contactPerson || '',
        phone: contractorCompany.phone?.replace('+47 ', '') || '', // Remove +47 prefix for display
        email: contractorCompany.email || '',
        streetAddress: contractorCompany.streetAddress || '',
        postalCode: contractorCompany.postalCode || '',
        city: contractorCompany.city || '',
        entrance: contractorCompany.entrance || '',
        notes: contractorCompany.notes || '',
        useCustomAddress: contractorCompany.useCustomAddress || false
      });
      setErrors({});
      setSuccessMessage('');
    }
  }, [isOpen, contractorCompany]);

  // Handle form field changes
  const handleFieldChange = (field: keyof CompanyFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Handle Brønnøysundregisteret refresh
  const handleRefreshBrreg = async () => {
    if (!formData.orgNumber) {
      setErrors({ orgNumber: companyProfileTexts.errors.brreg.orgNumberRequired });
      return;
    }

    setIsRefreshingBrreg(true);
    setErrors({});

    try {
      await searchByOrgNumber(formData.orgNumber);
    } catch (error) {
      setErrors({ general: companyProfileTexts.errors.brreg.fetchFailed });
    } finally {
      setIsRefreshingBrreg(false);
    }
  };

  // Update form data when Brønnøysundregisteret data is fetched
  useEffect(() => {
    if (selectedCompany && !isRefreshingBrreg) {
      const company = selectedCompany as CompanyInfo;
      
      setFormData(prev => ({
        ...prev,
        companyName: company.name || prev.companyName,
        streetAddress: company.visitingAddress?.street || company.businessAddress?.street || prev.streetAddress,
        postalCode: company.visitingAddress?.postalCode || company.businessAddress?.postalCode || prev.postalCode,
        city: company.visitingAddress?.city || company.businessAddress?.city || prev.city,
        // Update contact info if available from registry
        ...(company.registryContact?.phone && {
          phone: company.registryContact.phone.replace('+47', '').replace(/\s/g, '')
        }),
        ...(company.registryContact?.email && {
          email: company.registryContact.email
        })
      }));

      setSuccessMessage(companyProfileTexts.success.brregUpdated);
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000);
    }
  }, [selectedCompany, isRefreshingBrreg]);

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.companyName.trim()) {
      newErrors.companyName = companyProfileTexts.errors.required.companyName;
    }

    if (!formData.orgNumber.trim()) {
      newErrors.orgNumber = companyProfileTexts.errors.required.orgNumber;
    } else if (!/^\d{9}$/.test(formData.orgNumber.replace(/\s/g, ''))) {
      newErrors.orgNumber = companyProfileTexts.errors.format.orgNumber;
    }

    if (!formData.contactPerson.trim()) {
      newErrors.contactPerson = companyProfileTexts.errors.required.contactPerson;
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = companyProfileTexts.errors.format.email;
    }

    if (formData.phone && !/^\d{8}$/.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = companyProfileTexts.errors.format.phone;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || !contractorCompany || !user) {
      return;
    }

    setIsSaving(true);
    setErrors({});

    try {
      // Format phone number for storage
      const formattedPhone = formData.phone ? `+47 ${formData.phone}` : undefined;

      await updateCompany({
        clerkUserId: user.id,
        name: formData.companyName.trim(),
        contactPerson: formData.contactPerson.trim(),
        phone: formattedPhone,
        email: formData.email.trim() || undefined,
        streetAddress: formData.streetAddress.trim() || undefined,
        postalCode: formData.postalCode.trim() || undefined,
        city: formData.city.trim() || undefined,
        entrance: formData.entrance.trim() || undefined,
        orgNumber: formData.orgNumber.replace(/\s/g, ''),
        notes: formData.notes.trim() || undefined,
        useCustomAddress: formData.useCustomAddress
      });

      setSuccessMessage(companyProfileTexts.success.updated);

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Close modal after short delay
      setTimeout(() => {
        onClose();
      }, 1500);

    } catch (error: any) {
      console.error('Error updating company:', error);
      setErrors({
        general: error.message || companyProfileTexts.errors.general
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isSaving) {
      clearResults();
      onClose();
    }
  };

  // Don't render if not open or no company data
  if (!isOpen || !contractorCompany) {
    return null;
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={companyProfileTexts.modal.title}
      size="large"
      className="company-profile-modal"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Success Message */}
        {successMessage && (
          <div className="bg-jobblogg-success-soft border border-jobblogg-success text-jobblogg-success-dark px-4 py-3 rounded-lg">
            <TextMedium>{successMessage}</TextMedium>
          </div>
        )}

        {/* General Error */}
        {errors.general && (
          <div className="bg-jobblogg-error-soft border border-jobblogg-error text-jobblogg-error-dark px-4 py-3 rounded-lg">
            <TextMedium>{errors.general}</TextMedium>
          </div>
        )}

        {/* Brønnøysundregisteret Section */}
        <div className="bg-jobblogg-neutral rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <TextStrong>{companyProfileTexts.sections.brregUpdate}</TextStrong>
            <SecondaryButton
              type="button"
              onClick={handleRefreshBrreg}
              loading={isRefreshingBrreg}
              disabled={!formData.orgNumber || isRefreshingBrreg}
              size="sm"
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              }
            >
              {isRefreshingBrreg ? companyProfileTexts.buttons.updating : companyProfileTexts.buttons.update}
            </SecondaryButton>
          </div>
          <TextMuted>
            {companyProfileTexts.sections.brregDescription}
          </TextMuted>
        </div>

        {/* Company Information Section */}
        <div className="space-y-4">
          <TextStrong as="h3">{companyProfileTexts.sections.companyInfo}</TextStrong>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              label={companyProfileTexts.labels.companyName}
              value={formData.companyName}
              onChange={(value) => handleFieldChange('companyName', value)}
              error={errors.companyName}
              required
              placeholder={companyProfileTexts.placeholders.companyName}
            />

            <TextInput
              label={companyProfileTexts.labels.orgNumber}
              value={formData.orgNumber}
              onChange={(value) => handleFieldChange('orgNumber', value)}
              error={errors.orgNumber}
              required
              placeholder={companyProfileTexts.placeholders.orgNumber}
              maxLength={9}
            />
          </div>
        </div>

        {/* Contact Information Section */}
        <div className="space-y-4">
          <TextStrong as="h3">{companyProfileTexts.sections.contactInfo}</TextStrong>

          <TextInput
            label={companyProfileTexts.labels.contactPerson}
            value={formData.contactPerson}
            onChange={(value) => handleFieldChange('contactPerson', value)}
            error={errors.contactPerson}
            required
            placeholder={companyProfileTexts.placeholders.contactPerson}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <PhoneInput
              label={companyProfileTexts.labels.phone}
              value={formData.phone}
              onChange={(value) => handleFieldChange('phone', value)}
              error={errors.phone}
              placeholder={companyProfileTexts.placeholders.phone}
            />

            <TextInput
              label={companyProfileTexts.labels.email}
              type="email"
              value={formData.email}
              onChange={(value) => handleFieldChange('email', value)}
              error={errors.email}
              placeholder={companyProfileTexts.placeholders.email}
            />
          </div>
        </div>

        {/* Address Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <TextStrong as="h3">{companyProfileTexts.sections.address}</TextStrong>
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={formData.useCustomAddress}
                onChange={(e) => handleFieldChange('useCustomAddress', e.target.checked)}
                className="rounded border-jobblogg-border text-jobblogg-primary focus:ring-jobblogg-primary"
              />
              <TextMuted>{companyProfileTexts.labels.useCustomAddress}</TextMuted>
            </label>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              <TextInput
                label={companyProfileTexts.labels.streetAddress}
                value={formData.streetAddress}
                onChange={(value) => handleFieldChange('streetAddress', value)}
                error={errors.streetAddress}
                placeholder={companyProfileTexts.placeholders.streetAddress}
              />
            </div>

            <TextInput
              label={companyProfileTexts.labels.postalCode}
              value={formData.postalCode}
              onChange={(value) => handleFieldChange('postalCode', value)}
              error={errors.postalCode}
              placeholder={companyProfileTexts.placeholders.postalCode}
              maxLength={4}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              label={companyProfileTexts.labels.city}
              value={formData.city}
              onChange={(value) => handleFieldChange('city', value)}
              error={errors.city}
              placeholder={companyProfileTexts.placeholders.city}
            />

            <TextInput
              label={companyProfileTexts.labels.entrance}
              value={formData.entrance}
              onChange={(value) => handleFieldChange('entrance', value)}
              error={errors.entrance}
              placeholder={companyProfileTexts.placeholders.entrance}
            />
          </div>
        </div>

        {/* Notes Section */}
        <div className="space-y-4">
          <TextStrong as="h3">{companyProfileTexts.sections.notes}</TextStrong>
          <TextArea
            label={companyProfileTexts.labels.notes}
            value={formData.notes}
            onChange={(value) => handleFieldChange('notes', value)}
            error={errors.notes}
            placeholder={companyProfileTexts.placeholders.notes}
            rows={3}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-jobblogg-border">
          <PrimaryButton
            type="submit"
            loading={isSaving}
            disabled={isSaving}
            className="sm:order-2"
            icon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            }
          >
            {isSaving ? companyProfileTexts.buttons.saving : companyProfileTexts.buttons.save}
          </PrimaryButton>

          <SecondaryButton
            type="button"
            onClick={handleClose}
            disabled={isSaving}
            className="sm:order-1"
          >
            {companyProfileTexts.buttons.cancel}
          </SecondaryButton>
        </div>
      </form>
    </Modal>
  );
};

export default CompanyProfileModal;
