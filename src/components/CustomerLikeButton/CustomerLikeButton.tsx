import React, { useState, useEffect } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { TextMuted } from '../ui';
import { PersonalizedLikeDisplay } from './PersonalizedLikeDisplay';

interface CustomerLikeButtonProps {
  logEntryId: string;
  projectId: string;
  sharedId: string;
  customerSessionId: string;
  customerName?: string;
  customerEmail?: string;
  isArchived?: boolean;
  showCount?: boolean;
  /** Project customer name for personalized like display */
  projectCustomerName?: string;
  /** Detailed like information for personalized display */
  likeDetails?: Array<{
    customerSessionId: string;
    customerName?: string;
    createdAt: number;
  }>;
}

export const CustomerLikeButton: React.FC<CustomerLikeButtonProps> = ({
  logEntryId,
  projectId,
  sharedId,
  customerSessionId,
  customerName,
  customerEmail,
  isArchived = false,
  showCount = true,
  projectCustomerName,
  likeDetails
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get current like status for this customer
  const likeStatus = useQuery(
    api.imageLikes.getLikeStatus,
    { logEntryId: logEntryId as any, customerSessionId }
  );

  // Get total like count for this image
  const likeCount = useQuery(
    api.imageLikes.getLikeCount,
    { logEntryId: logEntryId as any }
  );

  const toggleLike = useMutation(api.imageLikes.toggleLike);

  const handleLikeToggle = async () => {
    if (isSubmitting || isArchived) return;

    setIsSubmitting(true);
    setIsAnimating(true);

    try {
      await toggleLike({
        logEntryId: logEntryId as any,
        projectId: projectId as any,
        sharedId,
        customerSessionId,
        customerName,
        customerEmail
      });

      // Animation duration
      setTimeout(() => setIsAnimating(false), 300);
    } catch (error) {
      console.error('Error toggling like:', error);
      setIsAnimating(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isArchived) {
    return null; // Don't show like button on archived projects
  }

  const isLiked = likeStatus?.liked || false;
  const totalLikes = likeCount?.count || 0;

  return (
    <div className="flex items-center gap-2">
      <button
        onClick={handleLikeToggle}
        disabled={isSubmitting}
        className={`
          group relative flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200
          ${isLiked 
            ? 'bg-jobblogg-error text-white shadow-lg' 
            : 'bg-white/90 backdrop-blur-sm text-jobblogg-text-medium hover:bg-jobblogg-error hover:text-white shadow-md'
          }
          ${isSubmitting ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:scale-110'}
          ${isAnimating ? 'animate-pulse scale-125' : ''}
        `}
        aria-label={isLiked ? 'Fjern like' : 'Lik dette bildet'}
        title={isLiked ? 'Fjern like' : 'Lik dette bildet'}
      >
        {/* Heart Icon */}
        <svg 
          className={`w-5 h-5 transition-all duration-200 ${isAnimating ? 'scale-125' : ''}`}
          fill={isLiked ? 'currentColor' : 'none'}
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={isLiked ? 0 : 2} 
            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" 
          />
        </svg>

        {/* Loading Spinner */}
        {isSubmitting && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin opacity-50"></div>
          </div>
        )}

        {/* Ripple Effect */}
        {isAnimating && (
          <div className="absolute inset-0 rounded-full bg-jobblogg-error opacity-30 animate-ping"></div>
        )}
      </button>

      {/* Enhanced Like Display */}
      {likeDetails ? (
        <PersonalizedLikeDisplay
          totalLikes={totalLikes}
          likes={likeDetails}
          currentCustomerSessionId={customerSessionId}
          projectCustomerName={projectCustomerName}
          showCount={showCount}
        />
      ) : (
        /* Fallback to original display if no detailed like information */
        showCount && totalLikes > 0 && (
          <TextMuted className="text-sm font-medium">
            {totalLikes} {totalLikes === 1 ? 'like' : 'likes'}
          </TextMuted>
        )
      )}
    </div>
  );
};

// Hook to generate and manage customer session ID with recovery mechanism
export const useCustomerSession = (projectSharedId?: string, projectId?: string) => {
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const generateSessionId = useMutation(api.imageLikes.generateCustomerSessionId);
  const findCustomerSession = useQuery(
    api.messages.findCustomerSessionForProject,
    projectId && sessionId ? {
      projectId: projectId as any,
      currentSessionId: sessionId
    } : "skip"
  );

  useEffect(() => {
    // Only run once
    if (isInitialized) return;

    // Check if we already have a session ID in localStorage
    const existingSessionId = localStorage.getItem('jobblogg_customer_session_id');

    // Also check for project-specific session ID as fallback
    const projectSessionKey = projectSharedId ? `jobblogg_customer_session_${projectSharedId}` : null;
    const projectSessionId = projectSessionKey ? localStorage.getItem(projectSessionKey) : null;

    console.log('🔍 Customer Session Debug:', {
      existingSessionId,
      projectSessionId,
      projectSharedId,
      hasExisting: !!(existingSessionId || projectSessionId),
      isInitialized
    });

    // Use existing session ID (prefer global, fallback to project-specific)
    const sessionToUse = existingSessionId || projectSessionId;

    if (sessionToUse) {
      console.log('✅ Using existing customer session ID:', sessionToUse);
      setSessionId(sessionToUse);

      // Ensure both global and project-specific storage are updated
      localStorage.setItem('jobblogg_customer_session_id', sessionToUse);
      if (projectSessionKey) {
        localStorage.setItem(projectSessionKey, sessionToUse);
      }

      setIsInitialized(true);
    } else {
      console.log('🆕 Generating new customer session ID...');
      // Generate a new session ID
      generateSessionId({}).then((result) => {
        const newSessionId = result.sessionId;
        console.log('✅ Generated new customer session ID:', newSessionId);
        setSessionId(newSessionId);

        // Store in both global and project-specific storage
        localStorage.setItem('jobblogg_customer_session_id', newSessionId);
        if (projectSessionKey) {
          localStorage.setItem(projectSessionKey, newSessionId);
        }

        setIsInitialized(true);
      }).catch((error) => {
        console.error('Failed to generate customer session ID:', error);
        setIsInitialized(true);
      });
    }
  }, [generateSessionId, isInitialized, projectSharedId]);

  // Handle session recovery if we find existing customer messages
  useEffect(() => {
    if (findCustomerSession && findCustomerSession.shouldRecover && findCustomerSession.existingSessionId) {
      console.log('🔄 Recovering customer session:', {
        currentSessionId: sessionId,
        recoveredSessionId: findCustomerSession.existingSessionId,
        messageCount: findCustomerSession.messageCount
      });

      // Update to use the recovered session ID
      setSessionId(findCustomerSession.existingSessionId);

      // Update localStorage with recovered session
      localStorage.setItem('jobblogg_customer_session_id', findCustomerSession.existingSessionId);
      if (projectSharedId) {
        localStorage.setItem(`jobblogg_customer_session_${projectSharedId}`, findCustomerSession.existingSessionId);
      }
    }
  }, [findCustomerSession, sessionId, projectSharedId]);

  return sessionId;
};

export default CustomerLikeButton;
