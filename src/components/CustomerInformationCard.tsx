import React, { useState } from 'react';
import { useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { 
  Heading3, 
  BodyText, 
  TextMuted, 
  PrimaryButton, 
  TextInput, 
  PhoneInput,
  FormError 
} from './ui';
import { AddressMapPreview } from './GoogleMaps/AddressMapPreview';

interface CustomerData {
  _id: any;
  name: string;
  type: 'privat' | 'bedrift' | 'firma';
  contactPerson?: string;
  phone?: string;
  email?: string;
  streetAddress?: string;
  postalCode?: string;
  city?: string;
  entrance?: string;
  address?: string; // Legacy field
  orgNumber?: string;
  notes?: string;

  useCustomAddress?: boolean;
}

interface CustomerInformationCardProps {
  customer: CustomerData;
  projectId: string;
  onCustomerUpdate?: (updatedCustomer: CustomerData) => void;
}

export const CustomerInformationCard: React.FC<CustomerInformationCardProps> = ({
  customer,
  projectId,
  onCustomerUpdate
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    contactPerson: customer.contactPerson || '',
    phone: customer.phone || '',
    email: customer.email || ''
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isLoading, setIsLoading] = useState(false);

  const updateCustomer = useMutation(api.customers.update);

  // Format customer address with fallback logic
  const formatCustomerAddress = (customerData: CustomerData): string => {
    // Try structured address fields first
    if (customerData.streetAddress && customerData.postalCode && customerData.city) {
      let address = customerData.streetAddress;

      // Add entrance if available
      if (customerData.entrance) {
        address += `, ${customerData.entrance}`;
      }

      // Add postal code and city
      address += `, ${customerData.postalCode} ${customerData.city}`;

      return address;
    }

    // Fallback to legacy address field
    if (customerData.address) {
      return customerData.address;
    }

    // No address available
    return 'Ingen adresse registrert';
  };

  // Get address components for map
  const getAddressComponents = (customerData: CustomerData) => {
    if (customerData.streetAddress && customerData.postalCode && customerData.city) {
      return {
        streetAddress: customerData.streetAddress,
        postalCode: customerData.postalCode,
        city: customerData.city
      };
    }
    return null;
  };



  // Validate edit form
  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    // Phone validation
    if (!editData.phone.trim()) {
      newErrors.phone = 'Telefonnummer er påkrevd';
    } else if (!/^[\+]?[0-9\s\-\(\)]{8,}$/.test(editData.phone.trim())) {
      newErrors.phone = 'Ugyldig telefonnummer format';
    }

    // Email validation
    if (!editData.email.trim()) {
      newErrors.email = 'E-postadresse er påkrevd';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(editData.email.trim())) {
      newErrors.email = 'Ugyldig e-postadresse format';
    }

    // Contact person validation (for business customers)
    if (customer.type === 'bedrift' && !editData.contactPerson.trim()) {
      newErrors.contactPerson = 'Kontaktperson er påkrevd for bedriftskunder';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle save changes
  const handleSave = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const updatedCustomer = await updateCustomer({
        customerId: customer._id as any,
        updates: {
          contactPerson: editData.contactPerson.trim() || undefined,
          phone: editData.phone.trim(),
          email: editData.email.trim()
        }
      });

      if (onCustomerUpdate) {
        onCustomerUpdate({ ...customer, ...editData });
      }

      setIsEditing(false);
      setErrors({});
    } catch (error) {
      console.error('Failed to update customer:', error);
      setErrors({ general: 'Kunne ikke oppdatere kundeinformasjon. Prøv igjen.' });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle cancel editing
  const handleCancel = () => {
    setEditData({
      contactPerson: customer.contactPerson || '',
      phone: customer.phone || '',
      email: customer.email || ''
    });
    setErrors({});
    setIsEditing(false);
  };

  const addressComponents = getAddressComponents(customer);

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <Heading3>Kundeinformasjon</Heading3>
        {!isEditing && (
          <PrimaryButton
            size="sm"
            variant="secondary"
            onClick={() => setIsEditing(true)}
            className="min-h-[44px]"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Rediger kundeinformasjon
          </PrimaryButton>
        )}
      </div>

      <div className="bg-jobblogg-neutral rounded-lg border border-jobblogg-border overflow-hidden">
        {/* Customer Identity Section */}
        <div className="p-4 sm:p-6 border-b border-jobblogg-border">
          <div className="flex items-center gap-2 mb-4">
            <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
            <h4 className="text-sm font-semibold text-jobblogg-text-strong">
              Kundeidentitet
            </h4>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Customer Name */}
            <div>
              <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                {customer.type === 'bedrift' || customer.type === 'firma' ? 'Bedriftsnavn' : 'Kundenavn'}
              </TextMuted>
              <BodyText className="font-medium">
                {customer.name}
              </BodyText>
            </div>

            {/* Organization Number (for companies) */}
            {(customer.type === 'bedrift' || customer.type === 'firma') && customer.orgNumber && (
              <div>
                <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                  Organisasjonsnummer
                </TextMuted>
                <BodyText className="font-mono">
                  {customer.orgNumber}
                </BodyText>
              </div>
            )}

            {/* Customer Address */}
            <div className="md:col-span-2">
              <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                Kundeadresse
              </TextMuted>
              <BodyText>
                {formatCustomerAddress(customer)}
              </BodyText>
            </div>
          </div>
        </div>



        {/* Project-Specific Contact Information Section */}
        <div className="p-4 sm:p-6">
          <div className="flex items-center gap-2 mb-4">
            <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <h4 className="text-sm font-semibold text-jobblogg-text-strong">
              Prosjektspesifikk kontaktinformasjon
            </h4>
          </div>

          {!isEditing ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Contact Person (for business customers) */}
              {(customer.type === 'bedrift' || customer.type === 'firma') && customer.contactPerson && (
                <div>
                  <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                    Kontaktperson
                  </TextMuted>
                  <BodyText>
                    {customer.contactPerson}
                  </BodyText>
                </div>
              )}

              {/* Phone Number */}
              {customer.phone && (
                <div>
                  <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                    Telefon
                  </TextMuted>
                  <BodyText>
                    <a
                      href={`tel:${customer.phone}`}
                      className="text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors duration-200 hover:underline"
                    >
                      {customer.phone}
                    </a>
                  </BodyText>
                </div>
              )}

              {/* Email */}
              {customer.email && (
                <div>
                  <TextMuted className="text-xs font-medium uppercase tracking-wide mb-1">
                    E-post
                  </TextMuted>
                  <BodyText>
                    <a
                      href={`mailto:${customer.email}`}
                      className="text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors duration-200 hover:underline"
                    >
                      {customer.email}
                    </a>
                  </BodyText>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {/* Editing Form */}
              {(customer.type === 'bedrift' || customer.type === 'firma') && (
                <TextInput
                  label="Kontaktperson"
                  placeholder="F.eks. Ola Nordmann"
                  fullWidth
                  value={editData.contactPerson}
                  onChange={(e) => setEditData({ ...editData, contactPerson: e.target.value })}
                  error={errors.contactPerson}
                  helperText="Kontaktperson for dette spesifikke prosjektet"
                />
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <PhoneInput
                  label="Telefon"
                  required
                  fullWidth
                  value={editData.phone}
                  onChange={(value) => setEditData({ ...editData, phone: value })}
                  error={errors.phone}
                  helperText="Telefonnummer for dette prosjektet"
                />

                <TextInput
                  label="E-post"
                  type="email"
                  placeholder="F.eks. <EMAIL>"
                  required
                  fullWidth
                  value={editData.email}
                  onChange={(e) => setEditData({ ...editData, email: e.target.value })}
                  error={errors.email}
                  helperText="E-postadresse for dette prosjektet"
                />
              </div>

              {/* Warning Message */}
              <div className="bg-jobblogg-warning-soft border border-jobblogg-warning/30 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <svg className="w-4 h-4 text-jobblogg-warning mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <div>
                    <p className="text-sm text-jobblogg-text-strong font-medium mb-1">
                      Viktig informasjon
                    </p>
                    <p className="text-sm text-jobblogg-text-medium">
                      Endringer i kontaktinformasjon oppdaterer kunderegisteret og påvirker kun prosjektspesifikke kontaktdetaljer.
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-2 pt-2">
                <PrimaryButton
                  onClick={handleSave}
                  disabled={isLoading}
                  className="flex-1 min-h-[44px]"
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Lagrer...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      Lagre endringer
                    </>
                  )}
                </PrimaryButton>
                <PrimaryButton
                  variant="secondary"
                  onClick={handleCancel}
                  disabled={isLoading}
                  className="flex-1 min-h-[44px]"
                >
                  Avbryt
                </PrimaryButton>
              </div>

              {/* General Error */}
              {errors.general && <FormError message={errors.general} />}
            </div>
          )}
        </div>

        {/* Address Map Section */}
        {addressComponents && (
          <div className="border-t border-jobblogg-border">
            <AddressMapPreview
              streetAddress={addressComponents.streetAddress}
              postalCode={addressComponents.postalCode}
              city={addressComponents.city}
              width={400}
              height={200}
              zoom={15}
              className="rounded-none"
            />
          </div>
        )}
      </div>
    </div>
  );
};
