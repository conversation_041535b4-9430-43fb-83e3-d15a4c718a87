/**
 * Test utility for company lookup functionality
 * Can be used in browser console to test the API integration
 */

import { searchCompanies, getCompanyByOrgNumber } from '../services/companyLookup';

/**
 * Test company search functionality
 * Usage in browser console: testCompanySearch('Equinor')
 */
export async function testCompanySearch(query: string) {
  console.log(`🔍 Testing company search for: "${query}"`);
  
  try {
    const result = await searchCompanies(query, 5);
    
    if (result.success) {
      console.log('✅ Search successful:', result.data);
      console.table(result.data.companies.map(company => ({
        name: company.name,
        orgNumber: company.organizationNumber,
        address: company.visitingAddress ? 
          `${company.visitingAddress.street}, ${company.visitingAddress.postalCode} ${company.visitingAddress.city}` : 
          'Ingen adresse',
        status: company.status
      })));
    } else {
      console.error('❌ Search failed:', result.error);
    }
  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

/**
 * Test company lookup by organization number
 * Usage in browser console: testOrgNumberLookup('*********')
 */
export async function testOrgNumberLookup(orgNumber: string) {
  console.log(`🔍 Testing org number lookup for: "${orgNumber}"`);
  
  try {
    const result = await getCompanyByOrgNumber(orgNumber);
    
    if (result.success) {
      console.log('✅ Lookup successful:', result.data);
      console.table({
        name: result.data.name,
        orgNumber: result.data.organizationNumber,
        visitingAddress: result.data.visitingAddress ? 
          `${result.data.visitingAddress.street}, ${result.data.visitingAddress.postalCode} ${result.data.visitingAddress.city}` : 
          'Ingen adresse',
        businessAddress: result.data.businessAddress ? 
          `${result.data.businessAddress.street}, ${result.data.businessAddress.postalCode} ${result.data.businessAddress.city}` : 
          'Ingen adresse',
        status: result.data.status,
        industry: result.data.industryDescription || 'Ukjent'
      });
    } else {
      console.error('❌ Lookup failed:', result.error);
    }
  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

/**
 * Test with known Norwegian companies
 */
export async function runTestSuite() {
  console.log('🧪 Running company lookup test suite...');
  
  // Test 1: Search for Equinor (large Norwegian company)
  await testCompanySearch('Equinor');
  
  // Test 2: Search for DNB (bank)
  await testCompanySearch('DNB');
  
  // Test 3: Lookup Equinor by org number
  await testOrgNumberLookup('*********'); // Equinor ASA
  
  // Test 4: Test invalid org number
  await testOrgNumberLookup('*********');
  
  // Test 5: Test empty search
  await testCompanySearch('');
  
  console.log('✅ Test suite completed');
}

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testCompanySearch = testCompanySearch;
  (window as any).testOrgNumberLookup = testOrgNumberLookup;
  (window as any).runTestSuite = runTestSuite;
}
