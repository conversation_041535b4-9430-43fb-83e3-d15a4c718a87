/**
 * Debug utility to inspect raw API responses from Brønnøysundregisteret
 * This helps identify address field mapping issues
 */

/**
 * Fetch and log raw API response for debugging
 */
export async function debugCompanySearch(query: string) {
  console.log(`🔍 [DEBUG] Raw API search for: "${query}"`);
  
  try {
    const apiUrl = new URL('https://data.brreg.no/enhetsregisteret/api/enheter');
    apiUrl.searchParams.set('navn', query.trim());
    apiUrl.searchParams.set('size', '5');
    apiUrl.searchParams.set('page', '0');
    
    console.log(`📡 [DEBUG] API URL: ${apiUrl.toString()}`);
    
    const response = await fetch(apiUrl.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'JobbLogg/1.0 (Debug Mode)'
      }
    });

    if (!response.ok) {
      console.error(`❌ [DEBUG] API Error: ${response.status} ${response.statusText}`);
      return;
    }

    const data = await response.json();
    console.log(`📊 [DEBUG] Raw API Response:`, data);
    
    if (data._embedded?.enheter) {
      console.log(`🏢 [DEBUG] Found ${data._embedded.enheter.length} companies:`);
      
      data._embedded.enheter.forEach((enhet: any, index: number) => {
        console.log(`\n--- Company ${index + 1}: ${enhet.navn} ---`);
        console.log(`Org.nr: ${enhet.organisasjonsnummer}`);
        console.log(`Status: ${enhet.organisasjonsform?.kode} (konkurs: ${enhet.konkurs}, underAvvikling: ${enhet.underAvvikling})`);
        
        // Debug address fields
        console.log(`\n📍 Address Fields:`);
        console.log(`beliggenhetsadresse:`, enhet.beliggenhetsadresse);
        console.log(`forretningsadresse:`, enhet.forretningsadresse);
        
        // Check what our mapping would produce
        if (enhet.beliggenhetsadresse) {
          const addr = enhet.beliggenhetsadresse;
          const mappedStreet = [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim();
          console.log(`🔄 Mapped visiting address:`);
          console.log(`  street: "${mappedStreet}"`);
          console.log(`  postalCode: "${addr.postnummer || ''}"`);
          console.log(`  city: "${addr.poststed || ''}"`);
          console.log(`  municipality: "${addr.kommune || ''}"`);
        } else {
          console.log(`❌ No beliggenhetsadresse found`);
        }
        
        if (enhet.forretningsadresse) {
          const addr = enhet.forretningsadresse;
          const mappedStreet = [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim();
          console.log(`🔄 Mapped business address:`);
          console.log(`  street: "${mappedStreet}"`);
          console.log(`  postalCode: "${addr.postnummer || ''}"`);
          console.log(`  city: "${addr.poststed || ''}"`);
          console.log(`  municipality: "${addr.kommune || ''}"`);
        } else {
          console.log(`❌ No forretningsadresse found`);
        }
      });
    } else {
      console.log(`❌ [DEBUG] No companies found in response`);
    }
    
  } catch (error) {
    console.error(`💥 [DEBUG] Error:`, error);
  }
}

/**
 * Debug specific company by organization number
 */
export async function debugCompanyByOrgNumber(orgNumber: string) {
  console.log(`🔍 [DEBUG] Raw API lookup for org number: "${orgNumber}"`);
  
  try {
    const cleanOrgNumber = orgNumber.replace(/\D/g, '');
    const apiUrl = `https://data.brreg.no/enhetsregisteret/api/enheter/${cleanOrgNumber}`;
    
    console.log(`📡 [DEBUG] API URL: ${apiUrl}`);
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'JobbLogg/1.0 (Debug Mode)'
      }
    });

    if (!response.ok) {
      console.error(`❌ [DEBUG] API Error: ${response.status} ${response.statusText}`);
      return;
    }

    const enhet = await response.json();
    console.log(`📊 [DEBUG] Raw API Response:`, enhet);
    
    console.log(`\n🏢 Company: ${enhet.navn}`);
    console.log(`Org.nr: ${enhet.organisasjonsnummer}`);
    
    // Debug address fields in detail
    console.log(`\n📍 Detailed Address Analysis:`);
    
    if (enhet.beliggenhetsadresse) {
      console.log(`✅ beliggenhetsadresse exists:`, enhet.beliggenhetsadresse);
      const addr = enhet.beliggenhetsadresse;
      
      console.log(`  Raw adresse array:`, addr.adresse);
      console.log(`  adresse[0]: "${addr.adresse?.[0] || 'undefined'}"`);
      console.log(`  adresse[1]: "${addr.adresse?.[1] || 'undefined'}"`);
      console.log(`  postnummer: "${addr.postnummer || 'undefined'}"`);
      console.log(`  poststed: "${addr.poststed || 'undefined'}"`);
      console.log(`  kommune: "${addr.kommune || 'undefined'}"`);
      
      const mappedStreet = [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim();
      console.log(`  🔄 Our mapped street: "${mappedStreet}"`);
      console.log(`  🔄 Street empty? ${mappedStreet === ''}`);
    } else {
      console.log(`❌ No beliggenhetsadresse`);
    }
    
    if (enhet.forretningsadresse) {
      console.log(`✅ forretningsadresse exists:`, enhet.forretningsadresse);
      const addr = enhet.forretningsadresse;
      
      console.log(`  Raw adresse array:`, addr.adresse);
      console.log(`  adresse[0]: "${addr.adresse?.[0] || 'undefined'}"`);
      console.log(`  adresse[1]: "${addr.adresse?.[1] || 'undefined'}"`);
      console.log(`  postnummer: "${addr.postnummer || 'undefined'}"`);
      console.log(`  poststed: "${addr.poststed || 'undefined'}"`);
      console.log(`  kommune: "${addr.kommune || 'undefined'}"`);
      
      const mappedStreet = [addr.adresse?.[0], addr.adresse?.[1]].filter(Boolean).join(' ').trim();
      console.log(`  🔄 Our mapped street: "${mappedStreet}"`);
      console.log(`  🔄 Street empty? ${mappedStreet === ''}`);
    } else {
      console.log(`❌ No forretningsadresse`);
    }
    
  } catch (error) {
    console.error(`💥 [DEBUG] Error:`, error);
  }
}

/**
 * Test known Norwegian companies
 */
export async function debugKnownCompanies() {
  console.log(`🧪 [DEBUG] Testing known Norwegian companies...`);
  
  const testCases = [
    { name: 'Equinor', orgNumber: '*********' },
    { name: 'DNB', orgNumber: '*********' },
    { name: 'Telenor', orgNumber: '*********' },
    { name: 'Norsk Hydro', orgNumber: '*********' }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n🔍 Testing ${testCase.name} (${testCase.orgNumber}):`);
    await debugCompanyByOrgNumber(testCase.orgNumber);
    
    // Small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).debugCompanySearch = debugCompanySearch;
  (window as any).debugCompanyByOrgNumber = debugCompanyByOrgNumber;
  (window as any).debugKnownCompanies = debugKnownCompanies;
}
