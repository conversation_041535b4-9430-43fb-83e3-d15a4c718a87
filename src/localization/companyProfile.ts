/**
 * Norwegian localization for Company Profile functionality
 * 
 * Contains all text strings used in the company profile modal,
 * form validation messages, and related UI components.
 */

export const companyProfileTexts = {
  // Modal titles and headers
  modal: {
    title: 'Bedriftsprofil',
    subtitle: 'Administrer bedriftsinformasjonen din',
    closeButton: 'Lukk',
  },

  // Section headers
  sections: {
    brregUpdate: 'Oppdater fra Brønnøysundregisteret',
    brregDescription: 'Hent oppdatert bedriftsinformasjon fra det offentlige registeret',
    companyInfo: 'Bedriftsinformasjon',
    contactInfo: 'Kontaktinformasjon',
    address: 'Adresse',
    notes: 'Notater',
  },

  // Form labels
  labels: {
    companyName: 'Bedriftsnavn',
    orgNumber: 'Organisasjonsnummer',
    contactPerson: 'Kontaktperson',
    phone: 'Telefonnummer',
    email: 'E-postadresse',
    streetAddress: 'Gateadresse',
    postalCode: 'Postnummer',
    city: 'Poststed',
    entrance: 'Inngang/etasje',
    notes: 'Tilleggsnotater',
    useCustomAddress: 'Bruk tilpasset adresse',
  },

  // Form placeholders
  placeholders: {
    companyName: 'Skriv inn bedriftsnavn',
    orgNumber: '*********',
    contactPerson: 'Skriv inn navn på kontaktperson',
    phone: 'XXX XX XXX',
    email: '<EMAIL>',
    streetAddress: 'Storgata 15',
    postalCode: '0123',
    city: 'Oslo',
    entrance: 'Oppgang A, 2. etasje',
    notes: 'Eventuelle tilleggsnotater om bedriften...',
  },

  // Buttons
  buttons: {
    save: 'Lagre endringer',
    saving: 'Lagrer...',
    cancel: 'Avbryt',
    update: 'Oppdater',
    updating: 'Oppdaterer...',
    refreshBrreg: 'Oppdater fra registeret',
    refreshingBrreg: 'Henter data...',
  },

  // Validation error messages
  errors: {
    required: {
      companyName: 'Bedriftsnavn er påkrevd',
      orgNumber: 'Organisasjonsnummer er påkrevd',
      contactPerson: 'Kontaktperson er påkrevd',
    },
    format: {
      orgNumber: 'Organisasjonsnummer må være 9 siffer',
      email: 'Ugyldig e-postadresse',
      phone: 'Telefonnummer må være 8 siffer',
    },
    brreg: {
      orgNumberRequired: 'Organisasjonsnummer er påkrevd for å oppdatere fra registeret',
      fetchFailed: 'Kunne ikke hente data fra Brønnøysundregisteret',
      notFound: 'Ingen bedrift funnet med dette organisasjonsnummeret',
      invalidOrgNumber: 'Ugyldig organisasjonsnummer',
    },
    general: 'En feil oppstod ved oppdatering av bedriftsprofil',
    network: 'Nettverksfeil. Sjekk internettforbindelsen din og prøv igjen.',
    unauthorized: 'Du har ikke tilgang til å redigere denne bedriftsprofilen',
    notFound: 'Bedriftsprofil ikke funnet',
  },

  // Success messages
  success: {
    updated: 'Bedriftsprofil oppdatert!',
    brregUpdated: 'Bedriftsinformasjon oppdatert fra Brønnøysundregisteret',
    saved: 'Endringer lagret',
  },

  // Loading states
  loading: {
    fetchingCompany: 'Henter bedriftsinformasjon...',
    savingChanges: 'Lagrer endringer...',
    refreshingBrreg: 'Oppdaterer fra Brønnøysundregisteret...',
    validating: 'Validerer...',
  },

  // Confirmation messages
  confirmations: {
    unsavedChanges: 'Du har ulagrede endringer. Er du sikker på at du vil lukke?',
    overwriteWithBrreg: 'Dette vil overskrive eksisterende adresseinformasjon med data fra registeret. Fortsette?',
  },

  // Help text
  help: {
    orgNumber: 'Organisasjonsnummer fra Brønnøysundregisteret (9 siffer)',
    customAddress: 'Aktiver for å bruke en annen adresse enn den som er registrert',
    brregUpdate: 'Henter oppdatert informasjon direkte fra Brønnøysundregisteret',
    contactPerson: 'Hovedkontakt for denne bedriften',
    entrance: 'Tilleggsinformasjon om inngang, etasje, eller andre adressedetaljer',
  },

  // Accessibility labels
  a11y: {
    closeModal: 'Lukk bedriftsprofil-modal',
    requiredField: 'Påkrevd felt',
    optionalField: 'Valgfritt felt',
    errorMessage: 'Feilmelding',
    successMessage: 'Suksessmelding',
    loadingSpinner: 'Laster innhold',
    brregUpdateButton: 'Oppdater bedriftsinformasjon fra Brønnøysundregisteret',
    customAddressToggle: 'Aktiver eller deaktiver tilpasset adresse',
  },

  // Status indicators
  status: {
    online: 'Tilkoblet',
    offline: 'Frakoblet',
    syncing: 'Synkroniserer...',
    synced: 'Synkronisert',
    error: 'Feil',
    lastUpdated: 'Sist oppdatert',
    brregLastFetched: 'Sist hentet fra registeret',
  },

  // Date formatting
  dates: {
    today: 'I dag',
    yesterday: 'I går',
    daysAgo: '{{count}} dager siden',
    weeksAgo: '{{count}} uker siden',
    monthsAgo: '{{count}} måneder siden',
    never: 'Aldri',
  },

  // Field validation hints
  hints: {
    orgNumber: 'Eksempel: *********',
    phone: 'Kun norske telefonnummer (8 siffer)',
    email: 'Vil bli brukt til viktige bedriftsmeldinger',
    address: 'Besøksadresse for bedriften',
    notes: 'Intern informasjon som ikke deles med kunder',
  },

  // UserButton menu integration
  userButton: {
    menuLabel: 'Bedriftsprofil',
    menuDescription: 'Administrer bedriftsinformasjon',
  },
} as const;

// Type for accessing nested text keys
export type CompanyProfileTextKey = keyof typeof companyProfileTexts;

// Helper function to get nested text values
export const getCompanyProfileText = (key: string): string => {
  const keys = key.split('.');
  let value: any = companyProfileTexts;
  
  for (const k of keys) {
    value = value?.[k];
    if (value === undefined) {
      console.warn(`Missing translation key: ${key}`);
      return key;
    }
  }
  
  return typeof value === 'string' ? value : key;
};

// Export individual sections for easier imports
export const {
  modal,
  sections,
  labels,
  placeholders,
  buttons,
  errors,
  success,
  loading,
  confirmations,
  help,
  a11y,
  status,
  dates,
  hints,
  userButton,
} = companyProfileTexts;
