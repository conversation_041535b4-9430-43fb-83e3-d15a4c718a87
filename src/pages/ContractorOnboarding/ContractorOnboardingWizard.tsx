import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation, Routes, Route, Navigate } from 'react-router-dom';
import { useMutation } from 'convex/react';
import { useUser, useClerk } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, StepProgress, WizardStep, SecondaryButton } from '../../components/ui';
import { Step1Introduction } from './steps/Step1Introduction';
import { Step2CompanyLookup } from './steps/Step2CompanyLookup';
import { Step3ContactDetails } from './steps/Step3ContactDetails';
import { Step4Confirmation } from './steps/Step4Confirmation';
import { useContractorOnboardingStore } from './hooks/useContractorOnboardingStore';
import { markContractorOnboardingCompleted, resetContractorOnboardingStatus, clearAllContractorOnboardingData } from '../../components/ContractorOnboardingGuardSimple';

const STORAGE_KEY = 'jobblogg-contractor-onboarding';

// Form data interface for the multi-step wizard
export interface ContractorOnboardingFormData {
  // Company data
  companyName: string;
  orgNumber: string;
  contactPerson: string;
  phone: string;
  email: string;
  // Enhanced address fields
  streetAddress: string;
  postalCode: string;
  city: string;
  entrance: string;
  notes: string;

  // Brønnøysundregisteret data tracking
  brregFetchedAt?: number;
  brregData?: any;
  useCustomAddress?: boolean;
  lockedFields?: {
    orgNumber?: boolean;
    address?: boolean;
  };
  companySelected?: boolean;
}

// Default form data
const defaultFormData: ContractorOnboardingFormData = {
  companyName: '',
  orgNumber: '',
  contactPerson: '',
  phone: '',
  email: '',
  streetAddress: '',
  postalCode: '',
  city: '',
  entrance: '',
  notes: '',
  useCustomAddress: false,
  lockedFields: {
    orgNumber: false,
    address: false
  },
  companySelected: false
};

/**
 * ContractorOnboardingWizard component
 * 
 * Multi-step wizard for contractor onboarding with:
 * - Introduction step
 * - Company lookup with Brønnøysundregisteret integration
 * - Contact details
 * - Confirmation and submission
 */
const ContractorOnboardingWizard: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useUser();
  const { signOut } = useClerk();
  const [showSuccess, setShowSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [createdCompanyId, setCreatedCompanyId] = useState<string | null>(null);
  const [redirectPath, setRedirectPath] = useState<string | null>(null);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);

  // Get form data from store
  const {
    formData,
    setFormData,
    currentStep,
    setCurrentStep,
    brregData,
    setBrregData,
    brregFetchedAt,
    setBrregFetchedAt,
    useCustomAddress,
    setUseCustomAddress,
    lockedFields,
    setLockedFields,
    companySelected,
    setCompanySelected,
    resetState
  } = useContractorOnboardingStore();

  // Convex mutations
  const createContractorCompany = useMutation(api.contractorOnboarding.createContractorCompany);
  const updateOnboardingStatus = useMutation(api.contractorOnboarding.updateContractorOnboardingStatus);

  // Parse redirect path from URL query parameters
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const redirect = params.get('redirect');
    if (redirect) {
      setRedirectPath(redirect);
    }
  }, [location.search]);

  // Define wizard steps
  const steps = [
    { id: 1, title: 'Introduksjon', description: 'Velkommen til JobbLogg', isCompleted: currentStep > 1, isActive: currentStep === 1 },
    { id: 2, title: 'Bedriftsinformasjon', description: 'Registrer din bedrift', isCompleted: currentStep > 2, isActive: currentStep === 2 },
    { id: 3, title: 'Kontaktdetaljer', description: 'Legg til kontaktinformasjon', isCompleted: currentStep > 3, isActive: currentStep === 3 },
    { id: 4, title: 'Bekreftelse', description: 'Gjennomgå og bekreft', isCompleted: currentStep > 4, isActive: currentStep === 4 }
  ];

  // Autosave functionality with debouncing
  const saveToLocalStorage = useCallback(() => {
    const dataToSave = {
      formData,
      currentStep,
      brregData,
      brregFetchedAt,
      useCustomAddress,
      lockedFields,
      companySelected,
      timestamp: Date.now()
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
  }, [formData, currentStep, brregData, brregFetchedAt, useCustomAddress, lockedFields, companySelected]);

  // Load saved data from localStorage
  useEffect(() => {
    const savedData = localStorage.getItem(STORAGE_KEY);
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        
        // Check if data is not too old (7 days)
        const now = Date.now();
        const dataAge = now - (parsedData.timestamp || 0);
        const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
        
        if (dataAge < maxAge) {
          // Migrate phone format if needed
          const formDataToSet = parsedData.formData || defaultFormData;
          if (formDataToSet.phone && formDataToSet.phone.includes('+47')) {
            // Extract digits from old format (+47 XXX XX XXX)
            const digits = formDataToSet.phone.replace(/\D/g, '');
            if (digits.startsWith('47') && digits.length >= 10) {
              // Remove the 47 prefix to get just the 8-digit number
              formDataToSet.phone = digits.substring(2);
            }
          }

          setFormData(formDataToSet);
          setCurrentStep(parsedData.currentStep || 1);
          setBrregData(parsedData.brregData || null);
          setBrregFetchedAt(parsedData.brregFetchedAt || null);
          setUseCustomAddress(parsedData.useCustomAddress || false);
          setLockedFields(parsedData.lockedFields || { orgNumber: false, address: false });
          setCompanySelected(parsedData.companySelected || false);
        } else {
          // Data is too old, clear it
          localStorage.removeItem(STORAGE_KEY);
        }
      } catch (error) {
        console.error('Error parsing saved onboarding data:', error);
        localStorage.removeItem(STORAGE_KEY);
      }
    }
  }, []);

  // Save data on form changes (debounced)
  useEffect(() => {
    const hasFormData = Object.values(formData).some(value => 
      typeof value === 'string' && value.trim() !== ''
    );

    if (!hasFormData) return;

    const debounceTimer = setTimeout(() => {
      saveToLocalStorage();
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [formData, saveToLocalStorage]);

  // Save data before unload
  useEffect(() => {
    const handleBeforeUnload = () => {
      saveToLocalStorage();
    };

    const handleUnload = () => {
      saveToLocalStorage();
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('unload', handleUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('unload', handleUnload);
    };
  }, [saveToLocalStorage]);

  // Clear localStorage on successful submission
  const clearSavedData = () => {
    localStorage.removeItem(STORAGE_KEY);
  };

  // Update form data
  const updateFormData = (updates: Partial<ContractorOnboardingFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  // Navigation functions
  const goToNextStep = () => {
    if (currentStep < 4) {
      // Save data immediately before navigating to ensure no data loss
      saveToLocalStorage();
      setCurrentStep(currentStep + 1);
      setErrors({});
      
      // Update URL to reflect current step
      navigate(`/contractor-onboarding/step/${currentStep + 1}${redirectPath ? `?redirect=${encodeURIComponent(redirectPath)}` : ''}`);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      saveToLocalStorage();
      setCurrentStep(currentStep - 1);
      setErrors({});
      
      // Update URL to reflect current step
      navigate(`/contractor-onboarding/step/${currentStep - 1}${redirectPath ? `?redirect=${encodeURIComponent(redirectPath)}` : ''}`);
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!user) return;

    setIsLoading(true);
    setErrors({});

    try {
      // Format phone number for storage (add +47 prefix to raw digits)
      const formattedPhone = formData.phone ? `+47 ${formData.phone}` : '';

      // Try to create contractor company
      const companyId = await createContractorCompany({
        clerkUserId: user.id,
        name: formData.companyName,
        contactPerson: formData.contactPerson,
        phone: formattedPhone,
        email: formData.email,
        streetAddress: formData.streetAddress,
        postalCode: formData.postalCode,
        city: formData.city,
        entrance: formData.entrance,
        orgNumber: formData.orgNumber,
        notes: formData.notes,
        brregFetchedAt: formData.brregFetchedAt,
        brregData: formData.brregData,
        useCustomAddress: formData.useCustomAddress
      });

      setCreatedCompanyId(companyId);

      // Try to update onboarding status (but don't fail if this doesn't work)
      try {
        await updateOnboardingStatus({
          clerkUserId: user.id,
          completed: true
        });
      } catch (statusError) {
        console.warn('Failed to update onboarding status in Convex, but continuing:', statusError);
      }

      // Mark onboarding as completed in localStorage (this is the primary method now)
      markContractorOnboardingCompleted(user.id);

      // Clear saved data
      clearSavedData();

      // Show success message
      setShowSuccess(true);

      // Redirect after a short delay
      setTimeout(() => {
        if (redirectPath) {
          navigate(redirectPath);
        } else {
          navigate('/');
        }
      }, 3000);
    } catch (error: any) {
      console.error('Error creating contractor company:', error);

      // Show user-friendly error message
      setErrors({
        submit: error.message || 'Det oppstod en feil ved registrering av bedriften. Vennligst prøv igjen.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Redirect to the appropriate step based on URL
  useEffect(() => {
    const path = location.pathname;
    const stepMatch = path.match(/\/contractor-onboarding\/step\/(\d+)/);
    
    if (stepMatch) {
      const stepNumber = parseInt(stepMatch[1], 10);
      if (stepNumber >= 1 && stepNumber <= 4 && stepNumber !== currentStep) {
        setCurrentStep(stepNumber);
      }
    } else if (path === '/contractor-onboarding') {
      // Redirect to step 1 if just at the base path
      navigate(`/contractor-onboarding/step/1${redirectPath ? `?redirect=${encodeURIComponent(redirectPath)}` : ''}`);
    }
  }, [location.pathname, currentStep, navigate, redirectPath]);

  // Handle cancel/abort onboarding
  const handleCancelOnboarding = async () => {
    if (!user?.id) return;

    try {
      console.log('[CancelOnboarding] Starting comprehensive data cleanup...');

      // 1. Clear all localStorage keys related to contractor onboarding
      const removedCount = clearAllContractorOnboardingData(user.id);
      console.log(`[CancelOnboarding] Cleared ${removedCount} localStorage keys`);

      // 2. Reset all component state using the store's resetState function
      resetState();
      console.log('[CancelOnboarding] Reset component state');

      // 4. Clear any potential browser form autofill data by resetting form elements
      // This helps prevent browser from remembering form values
      const forms = document.querySelectorAll('form');
      forms.forEach(form => {
        if (form.reset) {
          form.reset();
        }
      });
      console.log('[CancelOnboarding] Reset DOM forms');

      // 5. Clear any cached data that might persist
      if ('caches' in window) {
        try {
          const cacheNames = await caches.keys();
          await Promise.all(
            cacheNames.map(cacheName => caches.delete(cacheName))
          );
          console.log('[CancelOnboarding] Cleared browser caches');
        } catch (cacheError) {
          console.warn('[CancelOnboarding] Failed to clear caches:', cacheError);
        }
      }

      // 6. Sign out the user completely
      console.log('[CancelOnboarding] Signing out user...');
      await signOut();

      // 7. Redirect to sign-in page
      navigate('/sign-in');
      console.log('[CancelOnboarding] Redirected to sign-in');

    } catch (error) {
      console.error('[CancelOnboarding] Error during onboarding cancellation:', error);

      // Even if some steps fail, still try to sign out and redirect
      try {
        await signOut();
      } catch (signOutError) {
        console.error('[CancelOnboarding] Sign out also failed:', signOutError);
      }

      // Force redirect regardless of errors
      navigate('/sign-in');
    }
  };

  // Confirm cancel dialog
  const confirmCancelOnboarding = () => {
    setShowCancelConfirm(true);
  };

  return (
    <PageLayout containerWidth="narrow">
      {/* Success Message */}
      {showSuccess && (
        <div className="bg-jobblogg-success-soft rounded-xl p-8 text-center animate-scale-in">
          <div className="w-16 h-16 bg-jobblogg-success rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <div className="space-y-2">
            <h2 className="text-2xl font-bold text-jobblogg-text-strong">Registrering fullført!</h2>
            <p className="text-jobblogg-text-muted">Din bedrift er nå registrert i JobbLogg.</p>
            <p className="text-jobblogg-text-muted text-sm">Du blir nå videresendt...</p>
          </div>
        </div>
      )}

      {/* Main Wizard Content */}
      {!showSuccess && (
        <div className="max-w-3xl mx-auto">
          {/* Step Progress */}
          <StepProgress steps={steps} currentStep={currentStep} className="mb-8" />

          {/* Cancel Button */}
          <div className="flex justify-end mb-4">
            <SecondaryButton
              onClick={confirmCancelOnboarding}
              variant="destructive"
              size="small"
              disabled={isLoading}
            >
              Avbryt registrering
            </SecondaryButton>
          </div>

          {/* Wizard Form */}
          <div className="bg-white rounded-xl shadow-lg border border-jobblogg-border overflow-hidden">
            <div className="p-8">
              <Routes>
                <Route path="/" element={<Navigate to="step/1" replace />} />
                
                <Route path="step/1" element={
                  <Step1Introduction
                    onNext={goToNextStep}
                  />
                } />
                
                <Route path="step/2" element={
                  <Step2CompanyLookup
                    formData={formData}
                    updateFormData={updateFormData}
                    errors={errors}
                    onNext={goToNextStep}
                    onPrevious={goToPreviousStep}
                    brregData={brregData}
                    setBrregData={setBrregData}
                    brregFetchedAt={brregFetchedAt}
                    setBrregFetchedAt={setBrregFetchedAt}
                    useCustomAddress={useCustomAddress}
                    setUseCustomAddress={setUseCustomAddress}
                    lockedFields={lockedFields}
                    setLockedFields={setLockedFields}
                    companySelected={companySelected}
                    setCompanySelected={setCompanySelected}
                    setErrors={setErrors}
                  />
                } />
                
                <Route path="step/3" element={
                  <Step3ContactDetails
                    formData={formData}
                    updateFormData={updateFormData}
                    errors={errors}
                    onNext={goToNextStep}
                    onPrevious={goToPreviousStep}
                    setErrors={setErrors}
                    brregData={brregData}
                  />
                } />
                
                <Route path="step/4" element={
                  <Step4Confirmation
                    formData={formData}
                    errors={errors}
                    onPrevious={goToPreviousStep}
                    onSubmit={handleSubmit}
                    isLoading={isLoading}
                    brregData={brregData}
                  />
                } />
                
                <Route path="*" element={<Navigate to="step/1" replace />} />
              </Routes>
            </div>
          </div>
        </div>
      )}

      {/* Cancel Confirmation Dialog */}
      {showCancelConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md mx-4 shadow-xl">
            <div className="text-center space-y-4">
              <div className="w-12 h-12 bg-jobblogg-error-soft rounded-full flex items-center justify-center mx-auto">
                <svg className="w-6 h-6 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-2">
                  Avbryt registrering?
                </h3>
                <p className="text-jobblogg-text-muted text-sm">
                  Dette vil slette all registreringsinformasjon og logge deg ut. Du må starte på nytt neste gang du logger inn.
                </p>
              </div>
              <div className="flex gap-3 justify-center">
                <SecondaryButton
                  onClick={() => setShowCancelConfirm(false)}
                  size="small"
                >
                  Fortsett registrering
                </SecondaryButton>
                <SecondaryButton
                  onClick={handleCancelOnboarding}
                  variant="destructive"
                  size="small"
                >
                  Ja, avbryt
                </SecondaryButton>
              </div>
            </div>
          </div>
        </div>
      )}
    </PageLayout>
  );
};

export default ContractorOnboardingWizard;
