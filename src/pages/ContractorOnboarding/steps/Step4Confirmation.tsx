import React from 'react';
import { PrimaryButton, SecondaryButton, FormError, Heading3, BodyText, TextMuted } from '../../../components/ui';
import type { ContractorOnboardingFormData } from '../ContractorOnboardingWizard';
import type { CompanyInfo } from '../../../services/companyLookup';

interface Step4ConfirmationProps {
  formData: ContractorOnboardingFormData;
  errors: { [key: string]: string };
  onPrevious: () => void;
  onSubmit: () => void;
  isLoading: boolean;
  brregData?: CompanyInfo | null;
  retryCount?: number;
  maxRetries?: number;
  isRetrying?: boolean;
}

/**
 * Step 4: Confirmation and Submission
 * 
 * Final review of all entered information before submission:
 * - Company information summary
 * - Contact details summary
 * - Edit links to previous steps
 * - Final submission
 */
export const Step4Confirmation: React.FC<Step4ConfirmationProps> = ({
  formData,
  errors,
  onPrevious,
  onSubmit,
  isLoading,
  brregData,
  retryCount = 0,
  maxRetries = 3,
  isRetrying = false
}) => {
  // Get dynamic contact person label based on registry data (same logic as Step3)
  const getContactPersonLabel = (): string => {
    if (brregData?.managingDirector?.roleDescription) {
      return brregData.managingDirector.roleDescription;
    }
    return 'Kontaktperson';
  };

  // Get dynamic phone field label based on registry data source (same logic as Step3)
  const getPhoneLabel = (): string => {
    if (brregData?.registryContact?.phoneSource === 'mobil') {
      return 'Mobilnummer';
    } else if (brregData?.registryContact?.phoneSource === 'telefon') {
      return 'Telefonnummer';
    }
    return 'Mobilnummer'; // Default for manual entry
  };

  // Check if contact person is from registry (should be locked)
  const isContactPersonFromRegistry = (): boolean => {
    return !!(brregData?.managingDirector?.fullName &&
              formData.contactPerson === brregData.managingDirector.fullName);
  };

  // Check if phone is from registry (should show source indicator)
  const isPhoneFromRegistry = (): boolean => {
    return !!(brregData?.registryContact?.phone);
  };

  // Check if email is from registry (should show source indicator)
  const isEmailFromRegistry = (): boolean => {
    return !!(brregData?.registryContact?.email &&
              formData.email === brregData.registryContact.email);
  };
  // Format address for display
  const formatAddress = () => {
    const parts = [
      formData.streetAddress,
      formData.entrance,
      `${formData.postalCode} ${formData.city}`.trim()
    ].filter(Boolean);
    
    return parts.length > 0 ? parts.join(', ') : 'Ikke oppgitt';
  };

  // Format phone number for display (add +47 prefix to raw digits)
  const formatPhone = () => {
    if (!formData.phone) return 'Ikke oppgitt';

    // Format raw digits as +47 XXX XX XXX
    const digits = formData.phone.replace(/\D/g, '');
    if (digits.length === 8) {
      return `+47 ${digits.slice(0, 3)} ${digits.slice(3, 5)} ${digits.slice(5)}`;
    }

    return formData.phone; // Fallback to raw value if not 8 digits
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-jobblogg-text-strong mb-2">
          Bekreft registrering
        </h2>
        <p className="text-jobblogg-text-muted">
          Gjennomgå informasjonen nedenfor og bekreft at alt er korrekt
        </p>
      </div>

      {/* Company Information Summary */}
      <div className="bg-white rounded-xl border border-jobblogg-border p-6 space-y-6">
        <Heading3 className="text-lg font-semibold text-jobblogg-text-strong">
          Bedriftsinformasjon
        </Heading3>

        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <TextMuted className="text-sm font-medium mb-1">Bedriftsnavn</TextMuted>
            <BodyText className="text-jobblogg-text-strong">
              {formData.companyName || 'Ikke oppgitt'}
            </BodyText>
          </div>

          <div>
            <TextMuted className="text-sm font-medium mb-1">Organisasjonsnummer</TextMuted>
            <BodyText className="text-jobblogg-text-strong">
              {formData.orgNumber || 'Ikke oppgitt'}
            </BodyText>
          </div>

          <div className="md:col-span-2">
            <TextMuted className="text-sm font-medium mb-1">Besøksadresse</TextMuted>
            <BodyText className="text-jobblogg-text-strong">
              {formatAddress()}
            </BodyText>
          </div>

          {formData.notes && (
            <div className="md:col-span-2">
              <TextMuted className="text-sm font-medium mb-1">Notater</TextMuted>
              <BodyText className="text-jobblogg-text-strong">
                {formData.notes}
              </BodyText>
            </div>
          )}
        </div>

        {/* Brønnøysundregisteret Badge */}
        {formData.brregFetchedAt && (
          <div className="flex items-center gap-2 pt-2 border-t border-jobblogg-border">
            <div className="w-6 h-6 bg-jobblogg-success-soft rounded-full flex items-center justify-center">
              <svg className="w-3 h-3 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <TextMuted className="text-sm">
              Informasjon hentet fra Brønnøysundregisteret
            </TextMuted>
          </div>
        )}
      </div>

      {/* Contact Information Summary */}
      <div className="bg-white rounded-xl border border-jobblogg-border p-6 space-y-6">
        <div className="flex items-center justify-between">
          <Heading3 className="text-lg font-semibold text-jobblogg-text-strong">
            Kontaktinformasjon
          </Heading3>
          <button
            type="button"
            onClick={onPrevious}
            className="text-sm text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors duration-200 flex items-center gap-1"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Rediger informasjon
          </button>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <div className="flex items-center gap-2 mb-1">
              <TextMuted className="text-sm font-medium">{getContactPersonLabel()}</TextMuted>
              {isContactPersonFromRegistry() && (
                <div className="flex items-center gap-1">
                  <div className="w-4 h-4 bg-jobblogg-success-soft rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <TextMuted className="text-xs">Låst</TextMuted>
                </div>
              )}
            </div>
            <BodyText className="text-jobblogg-text-strong">
              {formData.contactPerson || 'Ikke oppgitt'}
            </BodyText>
            {isContactPersonFromRegistry() && (
              <TextMuted className="text-xs mt-1">
                Fra Brønnøysundregisteret
              </TextMuted>
            )}
          </div>

          <div>
            <div className="flex items-center gap-2 mb-1">
              <TextMuted className="text-sm font-medium">{getPhoneLabel()}</TextMuted>
              {isPhoneFromRegistry() && (
                <div className="flex items-center gap-1">
                  <div className="w-4 h-4 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
                    <span className="text-jobblogg-primary text-xs font-bold">Auto</span>
                  </div>
                </div>
              )}
            </div>
            <BodyText className="text-jobblogg-text-strong">
              {formatPhone()}
            </BodyText>
            {isPhoneFromRegistry() && (
              <TextMuted className="text-xs mt-1">
                Fra Brønnøysundregisteret ({brregData?.registryContact?.phoneSource === 'mobil' ? 'mobil' : 'telefon'})
              </TextMuted>
            )}
          </div>

          <div className="md:col-span-2">
            <div className="flex items-center gap-2 mb-1">
              <TextMuted className="text-sm font-medium">E-postadresse</TextMuted>
              {isEmailFromRegistry() && (
                <div className="flex items-center gap-1">
                  <div className="w-4 h-4 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
                    <span className="text-jobblogg-primary text-xs font-bold">Auto</span>
                  </div>
                </div>
              )}
            </div>
            <BodyText className="text-jobblogg-text-strong">
              {formData.email || 'Ikke oppgitt'}
            </BodyText>
            {isEmailFromRegistry() && (
              <TextMuted className="text-xs mt-1">
                Fra Brønnøysundregisteret
              </TextMuted>
            )}
          </div>
        </div>

        {/* Registry Contact Information Badge */}
        {(isContactPersonFromRegistry() || isPhoneFromRegistry() || isEmailFromRegistry()) && (
          <div className="flex items-center gap-2 pt-2 border-t border-jobblogg-border">
            <div className="w-6 h-6 bg-jobblogg-success-soft rounded-full flex items-center justify-center">
              <svg className="w-3 h-3 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <TextMuted className="text-sm">
              Kontaktinformasjon hentet fra Brønnøysundregisteret
            </TextMuted>
          </div>
        )}
      </div>

      {/* Terms and Conditions */}
      <div className="bg-jobblogg-neutral-secondary rounded-xl p-6">
        <div className="flex items-start gap-3">
          <div className="w-8 h-8 bg-jobblogg-primary-soft rounded-full flex items-center justify-center flex-shrink-0 mt-1">
            <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h4 className="font-semibold text-jobblogg-text-strong mb-2">
              Ved å registrere bedriften bekrefter du at:
            </h4>
            <div className="space-y-2 text-sm text-jobblogg-text-muted">
              <p>
                • All oppgitt informasjon er korrekt og oppdatert
              </p>
              <p>
                • Du har rett til å representere bedriften i JobbLogg
              </p>
              <p>
                • Du godtar at informasjonen kan deles med kunder i prosjekter du velger å dele
              </p>
              <p>
                • Du kan når som helst oppdatere eller slette bedriftsinformasjonen
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* What Happens Next */}
      <div className="bg-jobblogg-primary-soft rounded-xl p-6">
        <h4 className="font-semibold text-jobblogg-text-strong mb-3">
          Hva skjer etter registrering?
        </h4>
        <div className="space-y-3">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-jobblogg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-white text-xs font-bold">1</span>
            </div>
            <TextMuted className="text-sm">
              Din bedrift blir registrert i JobbLogg-systemet
            </TextMuted>
          </div>
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-jobblogg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-white text-xs font-bold">2</span>
            </div>
            <TextMuted className="text-sm">
              Du får tilgang til alle funksjoner i JobbLogg
            </TextMuted>
          </div>
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-jobblogg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-white text-xs font-bold">3</span>
            </div>
            <TextMuted className="text-sm">
              Du kan begynne å opprette og administrere prosjekter
            </TextMuted>
          </div>
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-jobblogg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-white text-xs font-bold">4</span>
            </div>
            <TextMuted className="text-sm">
              Ditt bedriftsnavn vises i chat-meldinger til kunder
            </TextMuted>
          </div>
        </div>
      </div>

      {/* Retry Status */}
      {isRetrying && retryCount > 0 && (
        <div className="bg-jobblogg-primary-soft rounded-xl p-4">
          <div className="flex items-center gap-3">
            <div className="w-5 h-5 border-2 border-jobblogg-primary border-t-transparent rounded-full animate-spin"></div>
            <div>
              <p className="text-sm font-medium text-jobblogg-text-strong">
                Autentiseringsproblem oppdaget
              </p>
              <p className="text-sm text-jobblogg-text-muted">
                Prøver automatisk igjen... (forsøk {retryCount}/{maxRetries})
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Form Errors */}
      {errors.submit && (
        <FormError message={errors.submit} />
      )}

      {/* Action Buttons */}
      <div className="flex justify-between pt-6">
        <SecondaryButton onClick={onPrevious} disabled={isLoading}>
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Tilbake
        </SecondaryButton>

        <PrimaryButton 
          onClick={onSubmit}
          disabled={isLoading}
          size="large"
          className="px-8"
        >
          {isLoading ? (
            <>
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              {isRetrying ? (
                `Prøver igjen... (${retryCount}/${maxRetries})`
              ) : (
                'Registrerer bedrift...'
              )}
            </>
          ) : (
            <>
              Registrer bedrift
              <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </>
          )}
        </PrimaryButton>
      </div>
    </div>
  );
};

export default Step4Confirmation;
