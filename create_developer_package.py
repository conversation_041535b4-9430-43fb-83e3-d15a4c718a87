#!/usr/bin/env python3
"""
JobbLogg Developer Package Creator
Pakker alle nødvendige filer for design og mobilresponsivitet i en zip-fil.
"""

import os
import zipfile
import shutil
from pathlib import Path
from datetime import datetime

def create_developer_package():
    """Opprett en zip-fil med alle nødvendige filer for design og mobilresponsivitet."""
    
    # Definer base directory (hvor scriptet kjører fra)
    base_dir = Path.cwd()
    
    # Sjekk at vi er i riktig directory (skal inneholde src/ og package.json)
    if not (base_dir / "src").exists() or not (base_dir / "package.json").exists():
        print("❌ Feil: Scriptet må kjøres fra JobbLogg rot-mappen")
        print("   Forventet struktur: src/, package.json, etc.")
        return False
    
    # Opprett output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = base_dir / f"developer_package_{timestamp}"
    zip_filename = f"jobblogg_design_mobile_{timestamp}.zip"
    
    print(f"🎯 Oppretter utviklerpakke: {zip_filename}")
    
    # Kritiske filer som må inkluderes
    critical_files = [
        # Konfigurasjon og styling
        "tailwind.config.js",
        "src/index.css",
        "package.json",
        "tsconfig.json",
        
        # Styling konfigurasjon
        "src/styles/clerkAppearance.ts",
        "src/styles/clerkLocalization.ts",
        
        # Dokumentasjon
        "PAGES_OVERVIEW.md",
        "README.md",
    ]
    
    # Kritiske mapper som må inkluderes helt
    critical_directories = [
        "src/components/ui",
    ]
    
    # Eksempel sider (viktige for responsivitet)
    example_pages = [
        "src/pages/Dashboard/Dashboard.tsx",
        "src/pages/ProjectLog/ProjectLog.tsx", 
        "src/pages/CreateProject/CreateProjectWizard.tsx",
        "src/pages/ProjectDetail/ProjectDetail.tsx",
    ]
    
    # Støttefiler (valgfrie men nyttige)
    support_files = [
        "tests/mobile-responsiveness.spec.ts",
        "src/utils/featureFlags.ts",
        "DEVELOPMENT_LOG.md",
    ]
    
    # Chat komponenter
    chat_components = [
        "src/components/chat",
        "src/components/ContractorLikeIndicator",
    ]
    
    try:
        # Opprett zip fil
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            
            print("📁 Legger til kritiske filer...")
            # Legg til kritiske filer
            for file_path in critical_files:
                full_path = base_dir / file_path
                if full_path.exists():
                    zipf.write(full_path, file_path)
                    print(f"   ✅ {file_path}")
                else:
                    print(f"   ⚠️  Mangler: {file_path}")
            
            print("📂 Legger til kritiske mapper...")
            # Legg til kritiske mapper
            for dir_path in critical_directories:
                full_dir = base_dir / dir_path
                if full_dir.exists():
                    for root, dirs, files in os.walk(full_dir):
                        for file in files:
                            file_path = Path(root) / file
                            arc_path = file_path.relative_to(base_dir)
                            zipf.write(file_path, arc_path)
                    print(f"   ✅ {dir_path}/ (hele mappen)")
                else:
                    print(f"   ⚠️  Mangler mappe: {dir_path}")
            
            print("📄 Legger til eksempel sider...")
            # Legg til eksempel sider
            for file_path in example_pages:
                full_path = base_dir / file_path
                if full_path.exists():
                    zipf.write(full_path, file_path)
                    print(f"   ✅ {file_path}")
                else:
                    print(f"   ⚠️  Mangler: {file_path}")
            
            print("🔧 Legger til støttefiler...")
            # Legg til støttefiler
            for file_path in support_files:
                full_path = base_dir / file_path
                if full_path.exists():
                    zipf.write(full_path, file_path)
                    print(f"   ✅ {file_path}")
                else:
                    print(f"   ⚠️  Mangler: {file_path}")
            
            print("💬 Legger til chat komponenter...")
            # Legg til chat komponenter
            for dir_path in chat_components:
                full_dir = base_dir / dir_path
                if full_dir.exists():
                    if full_dir.is_dir():
                        for root, dirs, files in os.walk(full_dir):
                            for file in files:
                                file_path = Path(root) / file
                                arc_path = file_path.relative_to(base_dir)
                                zipf.write(file_path, arc_path)
                        print(f"   ✅ {dir_path}/ (hele mappen)")
                    else:
                        zipf.write(full_dir, dir_path)
                        print(f"   ✅ {dir_path}")
                else:
                    print(f"   ⚠️  Mangler: {dir_path}")
            
            # Legg til en README for utvikleren
            readme_content = f"""# JobbLogg Design & Mobilresponsivitet - Utviklerpakke

Opprettet: {datetime.now().strftime("%d.%m.%Y %H:%M:%S")}

## 🚀 Kom i gang:

1. Pakk ut denne zip-filen
2. Kjør: `npm install`
3. Start dev server: `npm run dev`
4. Les PAGES_OVERVIEW.md for full oversikt

## 📱 Fokusområder:

- Mobile-first design (de fleste brukere på mobil)
- Touch targets minimum 44x44px
- Responsive wrapper pattern: `w-full px-4 sm:px-0 sm:max-w-2xl sm:mx-auto`
- JobbLogg design tokens (jobblogg-prefixed)
- Norsk målgruppe

## 🎯 Hovedutfordringer:

1. Wrapper pattern implementering
2. Touch target optimalisering  
3. Breakpoint responsivitet (xxs:352px, xs:378px)
4. Flex patterns (flex-wrap min-w-0)
5. Image responsivitet
6. Chat bubble design
7. Form input handling

## 📞 Kontakt:

Se PAGES_OVERVIEW.md for detaljert oversikt over alle filer og struktur.
"""
            
            zipf.writestr("README_DEVELOPER.md", readme_content)
            print("   ✅ README_DEVELOPER.md (opprettet)")
        
        print(f"\n🎉 Suksess! Utviklerpakke opprettet:")
        print(f"   📦 Fil: {zip_filename}")
        print(f"   📏 Størrelse: {os.path.getsize(zip_filename) / 1024 / 1024:.1f} MB")
        
        # Vis innhold
        print(f"\n📋 Innhold i zip-filen:")
        with zipfile.ZipFile(zip_filename, 'r') as zipf:
            file_list = zipf.namelist()
            for file in sorted(file_list):
                print(f"   📄 {file}")
        
        print(f"\n✅ Pakken er klar til å deles med utvikler!")
        return True
        
    except Exception as e:
        print(f"❌ Feil ved opprettelse av pakke: {e}")
        return False

if __name__ == "__main__":
    print("🎯 JobbLogg Developer Package Creator")
    print("=" * 50)
    
    success = create_developer_package()
    
    if success:
        print("\n🚀 Neste steg:")
        print("   1. Del zip-filen med utvikleren")
        print("   2. Gi kontekst om JobbLogg (håndverker-app)")
        print("   3. Fokuser på mobile-first design")
        print("   4. Nevn spesifikke responsivitets-utfordringer")
    else:
        print("\n❌ Noe gikk galt. Sjekk feilmeldingene over.")
